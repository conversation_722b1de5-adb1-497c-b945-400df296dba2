{"/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md", "file_hash": "13d4f555be58515756eecb5552b6fcf0", "file_size": 766, "modified_time": "2025-08-30T07:26:53.253298", "processed_time": "2025-09-02T09:39:18.682366", "processing_status": "success", "title": "1. **标题:** NVIDIA Jetson Thor模组为机器人和物理AI解锁实时推理能力。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md", "file_hash": "18319c4f5f05047ca90173d975045f35", "file_size": 2721, "modified_time": "2025-08-31T09:26:43.456654", "processed_time": "2025-09-02T09:39:18.912268", "processing_status": "success", "title": "1. **标题：** 智能门锁线上均价跌破900元，千元以下产品成销售主流。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md", "file_hash": "8f34e922778535c368e91b61927dbd33", "file_size": 12352, "modified_time": "2025-08-31T09:26:43.878119", "processed_time": "2025-09-02T09:39:18.952250", "processing_status": "success", "title": "1.  **标题：** 人形机器人推动视触觉传感器赛道火热，多家公司密集宣布融资。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md", "file_hash": "0bf89c0ad4394aa812356a62b06cdc28", "file_size": 3027, "modified_time": "2025-08-31T00:06:30.276331", "processed_time": "2025-09-02T09:39:19.022530", "processing_status": "success", "title": "**1. 标题：** 中国产x86 CPU首次应用于桌面AI PC，挑战Intel和AMD。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md", "file_hash": "d3ac2c798b39b95a076a0e0a1b1f7858", "file_size": 21048, "modified_time": "2025-08-31T19:06:42.992784", "processed_time": "2025-09-02T09:39:19.342228", "processing_status": "success", "title": "1.  **标题:** 美团提出X-SAM：统一框架突破SAM局限，横扫20+分割基准。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md", "file_hash": "483bc14c2d4dd917f25c7ce529232f2a", "processed_time": "2025-09-02T09:39:19.608621", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 40\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md", "file_hash": "7a47cdacac2fbd170c52d7ad7727ca1f", "file_size": 2207, "modified_time": "2025-08-31T00:06:30.333313", "processed_time": "2025-09-02T09:39:20.576128", "processing_status": "success", "title": "1.  **标题：** 黄仁勋称英伟达所有产品已全部售罄。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md", "file_hash": "0719935f1f6696b950474df7e2eef33a", "file_size": 5515, "modified_time": "2025-08-31T09:26:43.137541", "processed_time": "2025-09-02T09:39:20.576205", "processing_status": "success", "title": "1.  **标题:** <PERSON><PERSON>分享了使用 LLM 辅助编程的最佳实践。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md", "file_hash": "74dedc4cecf35323fbb4c61b0fba6e9b", "file_size": 2506, "modified_time": "2025-08-31T09:26:43.616081", "processed_time": "2025-09-02T09:39:20.653263", "processing_status": "success", "title": "1.  **标题:** 我国科学家在6G无线通信领域取得新突破。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md", "file_hash": "5ac3e2bca701f144329219fe7f2cb699", "file_size": 17473, "modified_time": "2025-08-31T19:06:43.133144", "processed_time": "2025-09-02T09:39:20.834312", "processing_status": "success", "title": "1. **标题:** 95后华人创业公司Memories.ai发布全球首个大视觉记忆模型，突破AI视觉记忆局限。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md", "file_hash": "5420b809deb4d2b50fd5d7f691526360", "processed_time": "2025-09-02T09:39:21.126996", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 38\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md", "file_hash": "97992d8d245cae77cc8ef0f5a0fde7ed", "file_size": 9611, "modified_time": "2025-08-31T00:06:30.146446", "processed_time": "2025-09-02T09:39:21.454882", "processing_status": "success", "title": "1.  **标题：** 国家出台多项促消费惠民生政策，提供补贴和补助。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md", "file_hash": "ec53693c2a829c7197faebec4acca72b", "processed_time": "2025-09-02T09:39:21.632425", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 38\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md", "file_hash": "414e0daa048a402665452945af577578", "processed_time": "2025-09-02T09:39:21.814293", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 38\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md", "file_hash": "d33fbc62474f1a9bd3e5db9faa74c132", "processed_time": "2025-09-02T09:39:21.993227", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 38\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md", "file_hash": "5fb3ef55a8378fd682e403625e538140", "processed_time": "2025-09-02T09:39:22.116761", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md", "file_hash": "6591749e2109b77a77719f27de384508", "file_size": 4715, "modified_time": "2025-08-31T17:16:42.979271", "processed_time": "2025-09-02T09:39:22.173746", "processing_status": "success", "title": "1.  **标题:** 国家发改委强调发展“人工智能+”要避免无序竞争，并提出多项支持政策。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md", "file_hash": "a17304ef0c8f810904dacb649c7ba584", "file_size": 796, "modified_time": "2025-08-30T07:26:53.222618", "processed_time": "2025-09-02T09:39:22.263002", "processing_status": "success", "title": "1.  **标题：** 国务院发布AI新政，探讨未来十年最大机遇。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md", "file_hash": "08277418f216ecb70c38e1e8afc36fb4", "file_size": 3915, "modified_time": "2025-08-31T00:06:30.083466", "processed_time": "2025-09-02T09:39:22.354259", "processing_status": "success", "title": "1.  **标题：** 任正非等入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单。", "content_type": "general_article", "importance_level": "中", "keywords": ["文档", "汇总", "内容"]}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md", "file_hash": "80f3894903361197a9019b133bc67805", "processed_time": "2025-09-02T09:39:22.354311", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md", "file_hash": "********************************", "processed_time": "2025-09-02T09:39:22.533857", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md", "file_hash": "1293a0834cf0c7cb3a8395547cdcd16c", "processed_time": "2025-09-02T09:39:22.533959", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md", "file_hash": "eb9d3c22da54c56de82939f7e920859d", "processed_time": "2025-09-02T09:39:22.534666", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md", "file_hash": "5081ac7c053061e38f22ec63d07b9820", "processed_time": "2025-09-02T09:39:22.711758", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md", "file_hash": "9bd78c29fbb8c030d4fa4c1b76583e31", "processed_time": "2025-09-02T09:39:22.711855", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md", "file_hash": "f318353c8db0dfed5914522a862db9f4", "processed_time": "2025-09-02T09:39:22.894921", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md", "file_hash": "4994c756a2c29ec0520873a00f6dbc1c", "processed_time": "2025-09-02T09:39:22.894959", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md", "file_hash": "e7e9d94e5a8589fd2293dd66d19f0e0b", "processed_time": "2025-09-02T09:39:22.895004", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md", "file_hash": "9bfd887ba8acf6e7c2fbb5c1e8479950", "processed_time": "2025-09-02T09:39:23.074335", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md", "file_hash": "522e1e8808c90b6455711166707885dc", "processed_time": "2025-09-02T09:39:23.074393", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 37\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md", "file_hash": "9a94f60b57d10def8b694c9ba155ae7e", "processed_time": "2025-09-02T09:39:23.143328", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md", "file_hash": "5cd33ec2c22d4ab64570f965dc15ae83", "processed_time": "2025-09-02T09:39:23.252517", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md", "file_hash": "5a083800cd375ba2815ce196b8820a79", "processed_time": "2025-09-02T09:39:23.252533", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md", "file_hash": "ad6befd5a9b4f20499b0c365bb936fe9", "processed_time": "2025-09-02T09:39:23.434985", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md", "file_hash": "b59aca56a557ace1e73d3e0211a28f6e", "processed_time": "2025-09-02T09:39:23.435132", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md", "file_hash": "386e8f12acedd228847f9e8c8b185808", "processed_time": "2025-09-02T09:39:23.436159", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md", "file_hash": "d303895b1f2ee018916fa8e0f6e0fd7c", "processed_time": "2025-09-02T09:39:23.613464", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md", "file_hash": "94844aefe38f165cb6aaedb5a16142bc", "processed_time": "2025-09-02T09:39:23.613513", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md", "file_hash": "80539ffb7e148914e94bf852115f623c", "processed_time": "2025-09-02T09:39:23.794461", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md", "file_hash": "649b0d524bccb10ca5c4e8479ac026b5", "processed_time": "2025-09-02T09:39:23.794517", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md", "file_hash": "d2bd9fdd28ea8b0d7c3a63bbeb108e8f", "processed_time": "2025-09-02T09:39:23.973522", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md", "file_hash": "4b754712ca97ef08905e70f29fa4a077", "processed_time": "2025-09-02T09:39:23.973576", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md", "file_hash": "ea81e67ee2634bdb5a0965208b322a4b", "processed_time": "2025-09-02T09:39:23.973774", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md", "file_hash": "b3525de701ca5c6113b590c174707767", "processed_time": "2025-09-02T09:39:24.043778", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md", "file_hash": "b3720a88d0bffa5734f397f80fe6ffb7", "processed_time": "2025-09-02T09:39:24.153795", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md", "file_hash": "451ff2eb7e3bec63f041402f7f84bdd4", "processed_time": "2025-09-02T09:39:24.334259", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md", "file_hash": "595bd9ecac896cff42c45b878cdb3e5d", "processed_time": "2025-09-02T09:39:24.334305", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md", "file_hash": "4f34b83bd60fba5bccfbe8e537cf09bc", "processed_time": "2025-09-02T09:39:24.515303", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md", "file_hash": "a7a9fda1cb3341bd84545cdb7afae034", "processed_time": "2025-09-02T09:39:24.515375", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md", "file_hash": "a8e982a05a874e2e716e68ffc88510b4", "processed_time": "2025-09-02T09:39:24.515420", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md", "file_hash": "4cc85a5dea429a5892cb27c325a7317f", "processed_time": "2025-09-02T09:39:24.694189", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md", "file_hash": "679a0bd67cdb59d64cc4154e4fcf8513", "processed_time": "2025-09-02T09:39:24.876371", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md", "file_hash": "b5f566071b22909c9d04bc0bac9631ca", "processed_time": "2025-09-02T09:39:24.876396", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md", "file_hash": "b7fcd54abf77f1145021bf0125b3446f", "processed_time": "2025-09-02T09:39:24.876420", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md", "file_hash": "1797e094133ce73fbe127a35c0e40372", "processed_time": "2025-09-02T09:39:24.876473", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md", "file_hash": "6750af661aefde4ea83321e0a279e46a", "processed_time": "2025-09-02T09:39:25.054330", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 35\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md", "file_hash": "3e57af8504be02c1795db429225c3199", "processed_time": "2025-09-02T09:39:25.236009", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 34\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md", "file_hash": "da4049e0fac54f2b5c2d87ba586c2507", "processed_time": "2025-09-02T09:39:25.237069", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 34\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md", "file_hash": "1fef81f4a399250f6391220973949be8", "processed_time": "2025-09-02T09:39:25.239441", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 34\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md", "file_hash": "1b6480582924d42210b856267405de9d", "processed_time": "2025-09-02T09:39:25.239831", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 34\n}\n]"}, "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md": {"file_path": "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md", "file_hash": "b2d381c8d1d35745a654322058d92b13", "processed_time": "2025-09-02T09:39:25.412586", "processing_status": "failed", "error_message": "429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 34\n}\n]"}}