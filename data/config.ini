[DEFAULT]
# 新闻摘要程序配置文件

[API]
# Google Gemini API配置
# api_key = 从环境变量GEMINI_API_KEY读取
model_name = gemini-2.0-flash-exp
temperature = 0.3
max_tokens = 2048

[MONITORING]
# 文件监控配置
watch_directory = /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox
scan_interval_minutes = 30
file_extensions = .md
ignore_hidden_files = true
ignore_temp_files = true

[PROCESSING]
# 处理配置
max_concurrent_files = 5
chunk_size_chars = 4000
retry_attempts = 3
retry_delay_seconds = 5
enable_batch_processing = true

[OUTPUT]
# 输出配置
output_directory = data/summaries
processed_file_tracker = data/processed.json
log_directory = logs
log_level = INFO
enable_markdown_reports = true

[ADVANCED]
# 高级配置
enable_real_time_monitoring = true
progress_update_interval = 10
memory_optimization = true
api_rate_limit_per_minute = 60
