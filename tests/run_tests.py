#!/usr/bin/env python3
"""
测试运行器

提供统一的测试运行和报告功能
"""

import unittest
import sys
import os
from pathlib import Path
import time
from io import StringIO
import argparse

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))


class ColoredTextTestResult(unittest.TextTestResult):
    """
    带颜色的测试结果输出类
    
    继承unittest.TextTestResult，为测试结果提供彩色输出功能。
    在支持ANSI颜色代码的终端中显示彩色测试结果，提升可读性。
    
    颜色方案：
    - 成功测试: 绿色 (32)
    - 失败测试: 黄色 (33) 
    - 错误测试: 红色 (31)
    
    Attributes:
        use_colors (bool): 是否使用颜色输出（基于终端支持检测）
    """
    
    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.use_colors = hasattr(stream, 'isatty') and stream.isatty()
    
    def _color_text(self, text, color_code):
        """给文本添加颜色"""
        if self.use_colors:
            return f"\033[{color_code}m{text}\033[0m"
        return text
    
    def addSuccess(self, test):
        super().addSuccess(test)
        if self.showAll:
            self.stream.writeln(self._color_text("ok", "32"))  # 绿色
        elif self.dots:
            self.stream.write(self._color_text(".", "32"))
            self.stream.flush()
    
    def addError(self, test, err):
        super().addError(test, err)
        if self.showAll:
            self.stream.writeln(self._color_text("ERROR", "31"))  # 红色
        elif self.dots:
            self.stream.write(self._color_text("E", "31"))
            self.stream.flush()
    
    def addFailure(self, test, err):
        super().addFailure(test, err)
        if self.showAll:
            self.stream.writeln(self._color_text("FAIL", "31"))  # 红色
        elif self.dots:
            self.stream.write(self._color_text("F", "31"))
            self.stream.flush()
    
    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        if self.showAll:
            self.stream.writeln(self._color_text("skipped", "33"))  # 黄色
        elif self.dots:
            self.stream.write(self._color_text("s", "33"))
            self.stream.flush()


class TestRunner:
    """测试运行器"""
    
    def __init__(self, verbosity=2, use_colors=True):
        self.verbosity = verbosity
        self.use_colors = use_colors
        self.test_modules = [
            'test_config',
            'test_gemini_client',
            'test_storage',
            # 可以添加更多测试模块
        ]
    
    def discover_tests(self, pattern='test_*.py'):
        """发现测试用例"""
        test_dir = Path(__file__).parent
        loader = unittest.TestLoader()
        
        # 发现所有测试
        suite = loader.discover(str(test_dir), pattern=pattern)
        return suite
    
    def run_specific_module(self, module_name):
        """运行特定模块的测试"""
        loader = unittest.TestLoader()
        
        try:
            # 导入测试模块
            module = __import__(module_name)
            suite = loader.loadTestsFromModule(module)
            return suite
        except ImportError as e:
            print(f"无法导入测试模块 {module_name}: {e}")
            return unittest.TestSuite()
    
    def run_tests(self, test_suite=None, module_name=None):
        """运行测试"""
        if module_name:
            suite = self.run_specific_module(module_name)
        elif test_suite:
            suite = test_suite
        else:
            suite = self.discover_tests()
        
        # 创建测试运行器
        if self.use_colors:
            runner = unittest.TextTestRunner(
                verbosity=self.verbosity,
                resultclass=ColoredTextTestResult
            )
        else:
            runner = unittest.TextTestRunner(verbosity=self.verbosity)
        
        # 记录开始时间
        start_time = time.time()
        
        print("=" * 70)
        print("新闻摘要程序测试套件")
        print("=" * 70)
        
        # 运行测试
        result = runner.run(suite)
        
        # 计算运行时间
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出总结
        self._print_summary(result, duration)
        
        return result
    
    def _print_summary(self, result, duration):
        """打印测试总结"""
        print("\n" + "=" * 70)
        print("测试总结")
        print("=" * 70)
        
        total_tests = result.testsRun
        failures = len(result.failures)
        errors = len(result.errors)
        skipped = len(result.skipped)
        success = total_tests - failures - errors - skipped
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {self._color_text(str(success), '32')}")
        print(f"失败: {self._color_text(str(failures), '31')}")
        print(f"错误: {self._color_text(str(errors), '31')}")
        print(f"跳过: {self._color_text(str(skipped), '33')}")
        print(f"运行时间: {duration:.2f}秒")
        
        if failures == 0 and errors == 0:
            print(f"\n{self._color_text('所有测试通过！', '32')}")
        else:
            print(f"\n{self._color_text('存在测试失败或错误', '31')}")
        
        # 计算成功率
        if total_tests > 0:
            success_rate = (success / total_tests) * 100
            print(f"成功率: {success_rate:.1f}%")
    
    def _color_text(self, text, color_code):
        """给文本添加颜色"""
        if self.use_colors:
            return f"\033[{color_code}m{text}\033[0m"
        return text
    
    def run_coverage_analysis(self):
        """运行代码覆盖率分析"""
        try:
            import coverage
            
            # 创建覆盖率对象
            cov = coverage.Coverage()
            cov.start()
            
            # 运行测试
            suite = self.discover_tests()
            runner = unittest.TextTestRunner(verbosity=0, stream=StringIO())
            result = runner.run(suite)
            
            # 停止覆盖率收集
            cov.stop()
            cov.save()
            
            print("\n" + "=" * 70)
            print("代码覆盖率报告")
            print("=" * 70)
            
            # 生成报告
            cov.report()
            
            return result
            
        except ImportError:
            print("未安装coverage包，跳过覆盖率分析")
            print("安装命令: pip install coverage")
            return None
    
    def generate_test_report(self, output_file="test_report.html"):
        """生成HTML测试报告"""
        try:
            import xmlrunner
            
            # 运行测试并生成XML报告
            suite = self.discover_tests()
            
            with open('test_results.xml', 'wb') as output:
                runner = xmlrunner.XMLTestRunner(output=output)
                result = runner.run(suite)
            
            print(f"XML测试报告已生成: test_results.xml")
            return result
            
        except ImportError:
            print("未安装xmlrunner包，无法生成XML报告")
            print("安装命令: pip install xmlrunner")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="新闻摘要程序测试运行器")
    
    parser.add_argument(
        '--module', '-m',
        type=str,
        help='运行特定模块的测试'
    )
    
    parser.add_argument(
        '--verbosity', '-v',
        type=int,
        choices=[0, 1, 2],
        default=2,
        help='测试输出详细程度 (0=静默, 1=简单, 2=详细)'
    )
    
    parser.add_argument(
        '--no-color',
        action='store_true',
        help='禁用彩色输出'
    )
    
    parser.add_argument(
        '--coverage',
        action='store_true',
        help='运行代码覆盖率分析'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='生成XML测试报告'
    )
    
    parser.add_argument(
        '--pattern',
        type=str,
        default='test_*.py',
        help='测试文件匹配模式'
    )
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner(
        verbosity=args.verbosity,
        use_colors=not args.no_color
    )
    
    try:
        if args.coverage:
            # 运行覆盖率分析
            result = runner.run_coverage_analysis()
        elif args.report:
            # 生成测试报告
            result = runner.generate_test_report()
        elif args.module:
            # 运行特定模块
            result = runner.run_tests(module_name=args.module)
        else:
            # 运行所有测试
            suite = runner.discover_tests(pattern=args.pattern)
            result = runner.run_tests(test_suite=suite)
        
        # 根据测试结果设置退出码
        if result and (result.failures or result.errors):
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"运行测试时发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
