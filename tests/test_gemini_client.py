#!/usr/bin/env python3
"""
Gemini API客户端模块测试
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from modules.gemini_client import (
    GeminiClient, ContentType, ContentTypeDetector, 
    PromptTemplateManager, SummaryResponse
)


class TestContentTypeDetector(unittest.TestCase):
    """内容类型检测器测试"""
    
    def test_detect_news_type(self):
        """测试新闻类型检测"""
        detector = ContentTypeDetector()
        
        # 新闻内容
        news_content = "今日新闻报道，据记者采访了解到，最新消息显示..."
        result = detector.detect_type(news_content, "news_article.md")
        self.assertEqual(result, ContentType.NEWS)
        
        # 英文新闻
        english_news = "Breaking news: journalist reports that according to sources..."
        result = detector.detect_type(english_news, "breaking_news.md")
        self.assertEqual(result, ContentType.NEWS)
    
    def test_detect_tech_type(self):
        """测试技术文章类型检测"""
        detector = ContentTypeDetector()
        
        # 技术内容
        tech_content = "Python编程教程：如何使用API进行开发，import requests..."
        result = detector.detect_type(tech_content, "python_tutorial.md")
        self.assertEqual(result, ContentType.TECH_ARTICLE)
        
        # 英文技术文章
        english_tech = "JavaScript function implementation using GitHub API..."
        result = detector.detect_type(english_tech, "js_dev_guide.md")
        self.assertEqual(result, ContentType.TECH_ARTICLE)
    
    def test_detect_general_type(self):
        """测试普通文章类型检测"""
        detector = ContentTypeDetector()
        
        # 普通文章
        general_content = "这是一篇关于生活的文章，讨论了一些日常话题..."
        result = detector.detect_type(general_content, "life_article.md")
        self.assertEqual(result, ContentType.GENERAL_ARTICLE)
    
    def test_detect_unknown_type(self):
        """测试未知类型检测"""
        detector = ContentTypeDetector()
        
        # 很短的内容
        short_content = "短文本"
        result = detector.detect_type(short_content, "short.md")
        self.assertEqual(result, ContentType.UNKNOWN)
    
    def test_filename_influence(self):
        """测试文件名对类型检测的影响"""
        detector = ContentTypeDetector()
        
        # 普通内容但文件名包含新闻关键词
        content = "这是一些普通的文本内容"
        result = detector.detect_type(content, "news_report.md")
        self.assertEqual(result, ContentType.NEWS)
        
        # 普通内容但文件名包含技术关键词
        result = detector.detect_type(content, "tech_guide.md")
        self.assertEqual(result, ContentType.TECH_ARTICLE)


class TestPromptTemplateManager(unittest.TestCase):
    """提示词模板管理器测试"""
    
    def test_get_news_prompt(self):
        """测试获取新闻提示词"""
        content = "测试新闻内容"
        prompt = PromptTemplateManager.get_prompt(ContentType.NEWS, content)
        
        self.assertIn("新闻汇总助手", prompt)
        self.assertIn("5W1H", prompt)
        self.assertIn(content, prompt)
        self.assertIn("JSON格式", prompt)
    
    def test_get_tech_prompt(self):
        """测试获取技术文章提示词"""
        content = "测试技术内容"
        prompt = PromptTemplateManager.get_prompt(ContentType.TECH_ARTICLE, content)
        
        self.assertIn("技术文章汇总助手", prompt)
        self.assertIn("技术概念", prompt)
        self.assertIn(content, prompt)
        self.assertIn("JSON格式", prompt)
    
    def test_get_general_prompt(self):
        """测试获取普通文章提示词"""
        content = "测试普通内容"
        prompt = PromptTemplateManager.get_prompt(ContentType.GENERAL_ARTICLE, content)
        
        self.assertIn("通用文章汇总助手", prompt)
        self.assertIn("主要观点", prompt)
        self.assertIn(content, prompt)
        self.assertIn("JSON格式", prompt)
    
    def test_unknown_type_fallback(self):
        """测试未知类型的回退处理"""
        content = "测试内容"
        prompt = PromptTemplateManager.get_prompt(ContentType.UNKNOWN, content)
        
        # 应该回退到普通文章模板
        self.assertIn("通用文章汇总助手", prompt)
        self.assertIn(content, prompt)


class TestSummaryResponse(unittest.TestCase):
    """汇总响应测试"""
    
    def test_summary_response_creation(self):
        """测试汇总响应创建"""
        response = SummaryResponse(
            title="测试标题",
            core_facts="核心事实",
            key_points=["要点1", "要点2"],
            importance_level="高",
            importance_reason="重要原因",
            keywords=["关键词1", "关键词2"],
            content_type="news",
            processing_status="success"
        )
        
        self.assertEqual(response.title, "测试标题")
        self.assertEqual(response.core_facts, "核心事实")
        self.assertEqual(len(response.key_points), 2)
        self.assertEqual(response.importance_level, "高")
        self.assertEqual(response.content_type, "news")
        self.assertEqual(response.processing_status, "success")
        self.assertIsNone(response.error_message)


class TestGeminiClient(unittest.TestCase):
    """Gemini客户端测试"""
    
    def setUp(self):
        """测试前准备"""
        self.api_key = "test_api_key"
        self.client = None
    
    @patch('modules.gemini_client.genai.configure')
    @patch('modules.gemini_client.genai.GenerativeModel')
    def test_client_initialization(self, mock_model, mock_configure):
        """测试客户端初始化"""
        client = GeminiClient(self.api_key)
        
        # 验证API配置被调用
        mock_configure.assert_called_once_with(api_key=self.api_key)
        
        # 验证模型被创建
        mock_model.assert_called_once()
        
        # 验证属性设置
        self.assertEqual(client.api_key, self.api_key)
        self.assertEqual(client.model_name, "gemini-2.0-flash-exp")
        self.assertEqual(client.retry_attempts, 3)
        self.assertEqual(client.retry_delay, 5)
    
    @patch('modules.gemini_client.genai.configure')
    @patch('modules.gemini_client.genai.GenerativeModel')
    def test_generate_summary_success(self, mock_model_class, mock_configure):
        """测试成功生成汇总"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.text = '''
        {
            "title": "测试标题",
            "core_facts": "核心事实",
            "key_points": ["要点1", "要点2"],
            "importance_level": "高",
            "importance_reason": "重要原因",
            "keywords": ["关键词1", "关键词2"]
        }
        '''
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(self.api_key)
        result = client.generate_summary("测试内容", "test.md")
        
        # 验证结果
        self.assertEqual(result.title, "测试标题")
        self.assertEqual(result.core_facts, "核心事实")
        self.assertEqual(len(result.key_points), 2)
        self.assertEqual(result.importance_level, "高")
        self.assertEqual(result.processing_status, "success")
    
    @patch('modules.gemini_client.genai.configure')
    @patch('modules.gemini_client.genai.GenerativeModel')
    def test_generate_summary_api_error(self, mock_model_class, mock_configure):
        """测试API错误处理"""
        mock_model = Mock()
        mock_model.generate_content.side_effect = Exception("API Error")
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(self.api_key)
        result = client.generate_summary("测试内容", "test.md")
        
        # 验证错误处理
        self.assertEqual(result.processing_status, "error")
        self.assertIsNotNone(result.error_message)
        self.assertIn("API Error", result.error_message)
    
    @patch('modules.gemini_client.genai.configure')
    @patch('modules.gemini_client.genai.GenerativeModel')
    def test_long_content_processing(self, mock_model_class, mock_configure):
        """测试长文档处理"""
        # 创建超长内容
        long_content = "测试内容 " * 20000  # 超过100000字符
        
        mock_response = Mock()
        mock_response.text = '''
        {
            "title": "长文档标题",
            "core_facts": "长文档核心事实",
            "key_points": ["要点1"],
            "importance_level": "中",
            "importance_reason": "长文档",
            "keywords": ["长文档"]
        }
        '''
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(self.api_key)
        result = client.generate_summary(long_content, "long_test.md")
        
        # 验证分块处理
        self.assertEqual(result.processing_status, "success_chunked")
        self.assertIsNotNone(result.title)
    
    @patch('modules.gemini_client.genai.configure')
    @patch('modules.gemini_client.genai.GenerativeModel')
    def test_retry_mechanism(self, mock_model_class, mock_configure):
        """测试重试机制"""
        mock_model = Mock()
        # 前两次调用失败，第三次成功
        mock_model.generate_content.side_effect = [
            Exception("Rate limit"),
            Exception("Network error"),
            Mock(text='{"title": "成功", "core_facts": "", "key_points": [], "importance_level": "低", "importance_reason": "", "keywords": []}')
        ]
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(self.api_key, retry_attempts=3)
        
        with patch('time.sleep'):  # 跳过实际等待
            result = client.generate_summary("测试内容", "test.md")
        
        # 验证重试成功
        self.assertEqual(result.title, "成功")
        self.assertEqual(mock_model.generate_content.call_count, 3)
    
    @patch('modules.gemini_client.genai.configure')
    @patch('modules.gemini_client.genai.GenerativeModel')
    def test_text_response_parsing(self, mock_model_class, mock_configure):
        """测试文本响应解析"""
        # 模拟非JSON响应
        mock_response = Mock()
        mock_response.text = "这是一个普通的文本响应，不是JSON格式"
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(self.api_key)
        result = client.generate_summary("测试内容", "test.md")
        
        # 验证文本解析
        self.assertEqual(result.processing_status, "success_text_parsed")
        self.assertIsNotNone(result.title)
        self.assertIsNotNone(result.core_facts)
    
    def test_chunk_content(self):
        """测试内容分块"""
        with patch('modules.gemini_client.genai.configure'), \
             patch('modules.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(self.api_key)
            
            # 测试短内容
            short_content = "短内容"
            chunks = client._chunk_content(short_content, 1000)
            self.assertEqual(len(chunks), 1)
            self.assertEqual(chunks[0], short_content)
            
            # 测试长内容
            long_content = "测试内容。" * 1000
            chunks = client._chunk_content(long_content, 100)
            self.assertGreater(len(chunks), 1)
            
            # 验证所有分块合并后等于原内容
            combined = "".join(chunks)
            self.assertEqual(combined, long_content)


class TestGeminiClientIntegration(unittest.TestCase):
    """Gemini客户端集成测试"""
    
    @patch('modules.gemini_client.genai.configure')
    @patch('modules.gemini_client.genai.GenerativeModel')
    def test_full_processing_pipeline(self, mock_model_class, mock_configure):
        """测试完整处理流程"""
        # 模拟完整的API响应
        mock_response = Mock()
        mock_response.text = '''
        {
            "title": "集成测试新闻",
            "core_facts": "这是一个集成测试的核心事实",
            "key_points": ["集成测试要点1", "集成测试要点2", "集成测试要点3"],
            "importance_level": "高",
            "importance_reason": "这是集成测试的重要原因",
            "keywords": ["集成测试", "新闻", "汇总"]
        }
        '''
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        # 创建客户端并处理
        client = GeminiClient("test_api_key")
        
        # 测试新闻内容
        news_content = "今日新闻报道，据记者了解，发生了重要事件..."
        result = client.generate_summary(news_content, "news_test.md")
        
        # 验证完整流程
        self.assertEqual(result.title, "集成测试新闻")
        self.assertEqual(result.content_type, ContentType.NEWS.value)
        self.assertEqual(result.processing_status, "success")
        self.assertEqual(len(result.key_points), 3)
        self.assertEqual(len(result.keywords), 3)
        self.assertIsNone(result.error_message)


if __name__ == '__main__':
    unittest.main()
