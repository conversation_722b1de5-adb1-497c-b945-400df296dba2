#!/usr/bin/env python3
"""
存储模块测试
"""

import unittest
import tempfile
import json
from pathlib import Path
from datetime import datetime
from unittest.mock import patch, Mock
import sys

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from modules.storage import Storage, ProcessedFileTracker, SummaryResult


class TestSummaryResult(unittest.TestCase):
    """汇总结果测试"""
    
    def test_summary_result_creation(self):
        """测试汇总结果创建"""
        result = SummaryResult(
            file_path="/test/file.md",
            title="测试标题",
            core_facts="核心事实",
            key_points=["要点1", "要点2"],
            importance_level="高",
            importance_reason="重要原因",
            keywords=["关键词1", "关键词2"],
            content_type="news",
            processing_status="success",
            summary_time=datetime.now().isoformat(),
            file_size=1024,
            file_hash="test_hash"
        )
        
        self.assertEqual(result.file_path, "/test/file.md")
        self.assertEqual(result.title, "测试标题")
        self.assertEqual(len(result.key_points), 2)
        self.assertEqual(result.importance_level, "高")
        self.assertEqual(result.content_type, "news")
        self.assertEqual(result.processing_status, "success")
        self.assertEqual(result.file_size, 1024)
        self.assertEqual(result.file_hash, "test_hash")
    
    def test_to_dict(self):
        """测试转换为字典"""
        result = SummaryResult(
            file_path="/test/file.md",
            title="测试标题",
            core_facts="核心事实",
            key_points=["要点1"],
            importance_level="中",
            importance_reason="原因",
            keywords=["关键词"],
            content_type="tech_article",
            processing_status="success",
            summary_time="2024-01-01T12:00:00",
            file_size=512,
            file_hash="hash123"
        )
        
        result_dict = result.to_dict()
        
        self.assertIsInstance(result_dict, dict)
        self.assertEqual(result_dict['file_path'], "/test/file.md")
        self.assertEqual(result_dict['title'], "测试标题")
        self.assertEqual(result_dict['content_type'], "tech_article")
    
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            'file_path': "/test/file.md",
            'title': "测试标题",
            'core_facts': "核心事实",
            'key_points': ["要点1"],
            'importance_level': "中",
            'importance_reason': "原因",
            'keywords': ["关键词"],
            'content_type': "general_article",
            'processing_status': "success",
            'summary_time': "2024-01-01T12:00:00",
            'file_size': 256,
            'file_hash': "hash456",
            'error_message': None
        }
        
        result = SummaryResult.from_dict(data)
        
        self.assertEqual(result.file_path, "/test/file.md")
        self.assertEqual(result.title, "测试标题")
        self.assertEqual(result.content_type, "general_article")
        self.assertEqual(result.file_size, 256)
        self.assertIsNone(result.error_message)


class TestProcessedFileTracker(unittest.TestCase):
    """已处理文件跟踪器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.tracker_file = Path(self.temp_dir) / "processed.json"
        self.test_file = Path(self.temp_dir) / "test.md"
        
        # 创建测试文件
        self.test_file.write_text("测试内容")
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_tracker_initialization(self):
        """测试跟踪器初始化"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        self.assertEqual(tracker.tracker_file, self.tracker_file)
        self.assertIsInstance(tracker._processed_files, dict)
    
    def test_get_file_hash(self):
        """测试文件哈希计算"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        hash1 = tracker.get_file_hash(self.test_file)
        self.assertIsInstance(hash1, str)
        self.assertGreater(len(hash1), 0)
        
        # 相同文件应该有相同哈希
        hash2 = tracker.get_file_hash(self.test_file)
        self.assertEqual(hash1, hash2)
        
        # 修改文件内容，哈希应该改变
        self.test_file.write_text("修改后的内容")
        hash3 = tracker.get_file_hash(self.test_file)
        self.assertNotEqual(hash1, hash3)
    
    def test_mark_processed(self):
        """测试标记文件已处理"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        # 创建测试汇总结果
        summary = SummaryResult(
            file_path=str(self.test_file),
            title="测试标题",
            core_facts="核心事实",
            key_points=["要点1"],
            importance_level="中",
            importance_reason="原因",
            keywords=["关键词"],
            content_type="news",
            processing_status="success",
            summary_time=datetime.now().isoformat(),
            file_size=1024,
            file_hash="test_hash"
        )
        
        tracker.mark_processed(self.test_file, summary)
        
        # 验证文件被标记为已处理
        file_key = str(self.test_file.absolute())
        self.assertIn(file_key, tracker._processed_files)
        
        record = tracker._processed_files[file_key]
        self.assertEqual(record['processing_status'], 'success')
        self.assertEqual(record['title'], '测试标题')
        self.assertEqual(record['content_type'], 'news')
    
    def test_mark_failed(self):
        """测试标记文件处理失败"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        error_message = "测试错误信息"
        tracker.mark_failed(self.test_file, error_message)
        
        # 验证失败记录
        file_key = str(self.test_file.absolute())
        self.assertIn(file_key, tracker._processed_files)
        
        record = tracker._processed_files[file_key]
        self.assertEqual(record['processing_status'], 'failed')
        self.assertEqual(record['error_message'], error_message)
    
    def test_is_processed_new_file(self):
        """测试检查新文件是否已处理"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        # 新文件应该返回False
        self.assertFalse(tracker.is_processed(self.test_file))
    
    def test_is_processed_existing_file(self):
        """测试检查已处理文件"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        # 先标记文件已处理
        tracker.mark_processed(self.test_file)
        
        # 应该返回True
        self.assertTrue(tracker.is_processed(self.test_file))
    
    def test_is_processed_modified_file(self):
        """测试检查修改后的文件"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        # 标记文件已处理
        tracker.mark_processed(self.test_file)
        self.assertTrue(tracker.is_processed(self.test_file))
        
        # 修改文件内容
        self.test_file.write_text("修改后的内容")
        
        # 应该返回False（需要重新处理）
        self.assertFalse(tracker.is_processed(self.test_file))
    
    def test_persistence(self):
        """测试数据持久化"""
        # 创建第一个跟踪器并添加记录
        tracker1 = ProcessedFileTracker(self.tracker_file)
        tracker1.mark_processed(self.test_file)
        
        # 创建第二个跟踪器，应该能加载之前的记录
        tracker2 = ProcessedFileTracker(self.tracker_file)
        self.assertTrue(tracker2.is_processed(self.test_file))
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        tracker = ProcessedFileTracker(self.tracker_file)
        
        # 添加一些记录
        tracker.mark_processed(self.test_file)
        
        test_file2 = Path(self.temp_dir) / "test2.md"
        test_file2.write_text("测试内容2")
        tracker.mark_failed(test_file2, "测试错误")
        
        stats = tracker.get_statistics()
        
        self.assertEqual(stats['total_processed'], 2)
        self.assertEqual(stats['successful'], 1)
        self.assertEqual(stats['failed'], 1)
        self.assertEqual(stats['success_rate'], 0.5)


class TestStorage(unittest.TestCase):
    """存储管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.output_dir = Path(self.temp_dir) / "summaries"
        self.tracker_file = Path(self.temp_dir) / "processed.json"
        
        self.storage = Storage(
            output_directory=str(self.output_dir),
            processed_file_tracker=str(self.tracker_file)
        )
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_storage_initialization(self):
        """测试存储管理器初始化"""
        self.assertEqual(self.storage.output_directory, self.output_dir)
        self.assertIsInstance(self.storage.processed_tracker, ProcessedFileTracker)
        self.assertTrue(self.output_dir.exists())
    
    def test_save_summary_success(self):
        """测试保存成功的汇总"""
        summary = SummaryResult(
            file_path="/test/file.md",
            title="测试标题",
            core_facts="核心事实",
            key_points=["要点1", "要点2"],
            importance_level="高",
            importance_reason="重要原因",
            keywords=["关键词1", "关键词2"],
            content_type="news",
            processing_status="success",
            summary_time=datetime.now().isoformat(),
            file_size=1024,
            file_hash="test_hash"
        )
        
        success = self.storage.save_summary(summary)
        
        self.assertTrue(success)
        
        # 验证文件被保存
        summary_files = list(self.output_dir.glob("summary_*.json"))
        self.assertEqual(len(summary_files), 1)
        
        # 验证文件内容
        with open(summary_files[0], 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        self.assertEqual(saved_data['title'], "测试标题")
        self.assertEqual(saved_data['content_type'], "news")
    
    def test_save_summary_error(self):
        """测试保存错误汇总"""
        summary = SummaryResult(
            file_path="/test/file.md",
            title="处理失败",
            core_facts="",
            key_points=[],
            importance_level="低",
            importance_reason="处理失败",
            keywords=["错误"],
            content_type="unknown",
            processing_status="error",
            summary_time=datetime.now().isoformat(),
            file_size=0,
            file_hash="",
            error_message="测试错误"
        )
        
        success = self.storage.save_summary(summary)
        
        self.assertTrue(success)
        
        # 验证错误记录被保存
        summary_files = list(self.output_dir.glob("summary_*.json"))
        self.assertEqual(len(summary_files), 1)
    
    def test_load_summaries(self):
        """测试加载汇总结果"""
        # 先保存一些汇总
        for i in range(3):
            summary = SummaryResult(
                file_path=f"/test/file{i}.md",
                title=f"测试标题{i}",
                core_facts=f"核心事实{i}",
                key_points=[f"要点{i}"],
                importance_level="中",
                importance_reason="测试",
                keywords=[f"关键词{i}"],
                content_type="news",
                processing_status="success",
                summary_time=datetime.now().isoformat(),
                file_size=1024,
                file_hash=f"hash{i}"
            )
            self.storage.save_summary(summary)
        
        # 加载汇总
        summaries = self.storage.load_summaries()
        
        self.assertEqual(len(summaries), 3)
        self.assertIsInstance(summaries[0], SummaryResult)
    
    def test_load_summaries_with_limit(self):
        """测试限制加载数量"""
        # 保存5个汇总
        for i in range(5):
            summary = SummaryResult(
                file_path=f"/test/file{i}.md",
                title=f"测试标题{i}",
                core_facts=f"核心事实{i}",
                key_points=[f"要点{i}"],
                importance_level="中",
                importance_reason="测试",
                keywords=[f"关键词{i}"],
                content_type="news",
                processing_status="success",
                summary_time=datetime.now().isoformat(),
                file_size=1024,
                file_hash=f"hash{i}"
            )
            self.storage.save_summary(summary)
        
        # 限制加载3个
        summaries = self.storage.load_summaries(limit=3)
        
        self.assertEqual(len(summaries), 3)
    
    def test_generate_markdown_report(self):
        """测试生成Markdown报告"""
        # 保存一个汇总
        summary = SummaryResult(
            file_path="/test/file.md",
            title="测试新闻",
            core_facts="重要事件发生",
            key_points=["要点1", "要点2"],
            importance_level="高",
            importance_reason="影响重大",
            keywords=["新闻", "重要"],
            content_type="news",
            processing_status="success",
            summary_time=datetime.now().isoformat(),
            file_size=1024,
            file_hash="test_hash"
        )
        self.storage.save_summary(summary)
        
        # 生成报告
        report = self.storage.generate_report("markdown")
        
        self.assertIn("# 新闻摘要报告", report)
        self.assertIn("## 统计信息", report)
        self.assertIn("## 最近汇总", report)
        self.assertIn("测试新闻", report)
        self.assertIn("重要事件发生", report)
    
    def test_generate_json_report(self):
        """测试生成JSON报告"""
        # 保存一个汇总
        summary = SummaryResult(
            file_path="/test/file.md",
            title="测试文章",
            core_facts="技术内容",
            key_points=["技术要点"],
            importance_level="中",
            importance_reason="技术价值",
            keywords=["技术"],
            content_type="tech_article",
            processing_status="success",
            summary_time=datetime.now().isoformat(),
            file_size=2048,
            file_hash="tech_hash"
        )
        self.storage.save_summary(summary)
        
        # 生成JSON报告
        report = self.storage.generate_report("json")
        
        # 验证JSON格式
        report_data = json.loads(report)
        
        self.assertIn('generated_time', report_data)
        self.assertIn('statistics', report_data)
        self.assertIn('recent_summaries', report_data)
        self.assertEqual(len(report_data['recent_summaries']), 1)
        self.assertEqual(report_data['recent_summaries'][0]['title'], "测试文章")
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        # 保存一些汇总
        for i in range(3):
            summary = SummaryResult(
                file_path=f"/test/file{i}.md",
                title=f"测试标题{i}",
                core_facts=f"核心事实{i}",
                key_points=[f"要点{i}"],
                importance_level="中",
                importance_reason="测试",
                keywords=[f"关键词{i}"],
                content_type="news",
                processing_status="success",
                summary_time=datetime.now().isoformat(),
                file_size=1024,
                file_hash=f"hash{i}"
            )
            self.storage.save_summary(summary)
        
        stats = self.storage.get_statistics()
        
        self.assertIn('total_processed', stats)
        self.assertIn('successful', stats)
        self.assertIn('api_calls', stats)
        self.assertIn('total_summaries', stats)
        self.assertIn('output_directory', stats)
        self.assertEqual(stats['total_summaries'], 3)
    
    def test_cleanup_old_files(self):
        """测试清理旧文件"""
        # 创建一些测试文件
        for i in range(3):
            test_file = self.output_dir / f"summary_old_{i}.json"
            test_file.write_text('{"test": "data"}')
        
        # 模拟旧文件（修改时间戳）
        import time
        old_time = time.time() - (40 * 24 * 3600)  # 40天前
        for file in self.output_dir.glob("summary_old_*.json"):
            os.utime(file, (old_time, old_time))
        
        # 清理30天前的文件
        cleaned_count = self.storage.cleanup_old_files(days=30)
        
        self.assertEqual(cleaned_count, 3)
        
        # 验证文件被删除
        remaining_files = list(self.output_dir.glob("summary_old_*.json"))
        self.assertEqual(len(remaining_files), 0)


if __name__ == '__main__':
    unittest.main()
