#!/usr/bin/env python3
"""
配置管理模块测试
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, mock_open

# 添加项目根目录到路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import Config, setup_logging


class TestConfig(unittest.TestCase):
    """配置管理测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.ini"
        self.env_file = Path(self.temp_dir) / ".env"
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_config(self):
        """测试默认配置"""
        config = Config()
        
        # 检查默认值
        self.assertEqual(config.model_name, "gemini-2.0-flash-exp")
        self.assertEqual(config.temperature, 0.3)
        self.assertEqual(config.max_tokens, 2048)
        self.assertEqual(config.scan_interval_minutes, 30)
        self.assertEqual(config.max_concurrent_files, 5)
        self.assertEqual(config.chunk_size_chars, 4000)
        self.assertEqual(config.retry_attempts, 3)
        self.assertEqual(config.log_level, "INFO")
        self.assertTrue(config.enable_real_time_monitoring)
        self.assertTrue(config.enable_batch_processing)
    
    def test_load_from_env(self):
        """测试从环境变量加载配置"""
        # 设置环境变量
        test_env = {
            'GEMINI_API_KEY': 'test_api_key',
            'MODEL_NAME': 'test_model',
            'TEMPERATURE': '0.5',
            'MAX_TOKENS': '1024',
            'WATCH_DIRECTORY': '/test/path',
            'SCAN_INTERVAL_MINUTES': '60',
            'MAX_CONCURRENT_FILES': '10',
            'CHUNK_SIZE_CHARS': '8000',
            'RETRY_ATTEMPTS': '5',
            'OUTPUT_DIRECTORY': '/test/output',
            'LOG_LEVEL': 'DEBUG',
            'ENABLE_REAL_TIME_MONITORING': 'false',
            'ENABLE_BATCH_PROCESSING': 'true'
        }
        
        with patch.dict(os.environ, test_env):
            config = Config()
            config.load_from_env()
            
            # 验证环境变量被正确加载
            self.assertEqual(config.gemini_api_key, 'test_api_key')
            self.assertEqual(config.model_name, 'test_model')
            self.assertEqual(config.temperature, 0.5)
            self.assertEqual(config.max_tokens, 1024)
            self.assertEqual(config.watch_directory, '/test/path')
            self.assertEqual(config.scan_interval_minutes, 60)
            self.assertEqual(config.max_concurrent_files, 10)
            self.assertEqual(config.chunk_size_chars, 8000)
            self.assertEqual(config.retry_attempts, 5)
            self.assertEqual(config.output_directory, '/test/output')
            self.assertEqual(config.log_level, 'DEBUG')
            self.assertFalse(config.enable_real_time_monitoring)
            self.assertTrue(config.enable_batch_processing)
    
    def test_load_from_file(self):
        """测试从配置文件加载"""
        # 创建测试配置文件
        config_content = """
[API]
model_name = test_model_from_file
temperature = 0.7
max_tokens = 512

[MONITORING]
watch_directory = /file/test/path
scan_interval_minutes = 45
file_extensions = .md,.txt
ignore_hidden_files = false

[PROCESSING]
max_concurrent_files = 8
chunk_size_chars = 6000
retry_attempts = 2
enable_batch_processing = false

[OUTPUT]
output_directory = /file/test/output
log_level = WARNING
enable_markdown_reports = false

[ADVANCED]
enable_real_time_monitoring = false
progress_update_interval = 5
memory_optimization = false
api_rate_limit_per_minute = 30
"""
        
        with open(self.config_file, 'w') as f:
            f.write(config_content)
        
        config = Config()
        config.load_from_file(str(self.config_file))
        
        # 验证配置文件被正确加载
        self.assertEqual(config.model_name, 'test_model_from_file')
        self.assertEqual(config.temperature, 0.7)
        self.assertEqual(config.max_tokens, 512)
        self.assertEqual(config.watch_directory, '/file/test/path')
        self.assertEqual(config.scan_interval_minutes, 45)
        self.assertEqual(config.file_extensions, ['.md', '.txt'])
        self.assertFalse(config.ignore_hidden_files)
        self.assertEqual(config.max_concurrent_files, 8)
        self.assertEqual(config.chunk_size_chars, 6000)
        self.assertEqual(config.retry_attempts, 2)
        self.assertFalse(config.enable_batch_processing)
        self.assertEqual(config.output_directory, '/file/test/output')
        self.assertEqual(config.log_level, 'WARNING')
        self.assertFalse(config.enable_markdown_reports)
        self.assertFalse(config.enable_real_time_monitoring)
        self.assertEqual(config.progress_update_interval, 5)
        self.assertFalse(config.memory_optimization)
        self.assertEqual(config.api_rate_limit_per_minute, 30)
    
    def test_validate_success(self):
        """测试配置验证成功"""
        config = Config()
        config.gemini_api_key = "valid_api_key"
        config.watch_directory = str(self.temp_dir)  # 使用存在的目录
        
        self.assertTrue(config.validate())
    
    def test_validate_missing_api_key(self):
        """测试缺少API密钥的验证"""
        config = Config()
        config.gemini_api_key = ""
        config.watch_directory = str(self.temp_dir)
        
        self.assertFalse(config.validate())
    
    def test_validate_invalid_directory(self):
        """测试无效目录的验证"""
        config = Config()
        config.gemini_api_key = "valid_api_key"
        config.watch_directory = "/nonexistent/directory"
        
        self.assertFalse(config.validate())
    
    def test_validate_invalid_values(self):
        """测试无效数值的验证"""
        config = Config()
        config.gemini_api_key = "valid_api_key"
        config.watch_directory = str(self.temp_dir)
        
        # 测试负数值
        config.scan_interval_minutes = -1
        self.assertFalse(config.validate())
        
        config.scan_interval_minutes = 30
        config.max_concurrent_files = 0
        self.assertFalse(config.validate())
        
        config.max_concurrent_files = 5
        config.chunk_size_chars = -100
        self.assertFalse(config.validate())
        
        config.chunk_size_chars = 4000
        config.retry_attempts = -1
        self.assertFalse(config.validate())
    
    def test_validate_invalid_log_level(self):
        """测试无效日志级别的验证"""
        config = Config()
        config.gemini_api_key = "valid_api_key"
        config.watch_directory = str(self.temp_dir)
        config.log_level = "INVALID_LEVEL"
        
        self.assertFalse(config.validate())
    
    def test_validate_empty_file_extensions(self):
        """测试空文件扩展名列表的验证"""
        config = Config()
        config.gemini_api_key = "valid_api_key"
        config.watch_directory = str(self.temp_dir)
        config.file_extensions = []
        
        self.assertFalse(config.validate())
    
    def test_create_directories(self):
        """测试创建目录"""
        config = Config()
        config.output_directory = str(Path(self.temp_dir) / "output")
        config.log_directory = str(Path(self.temp_dir) / "logs")
        config.processed_file_tracker = str(Path(self.temp_dir) / "data" / "processed.json")
        
        config.create_directories()
        
        # 验证目录被创建
        self.assertTrue(Path(config.output_directory).exists())
        self.assertTrue(Path(config.log_directory).exists())
        self.assertTrue(Path(config.processed_file_tracker).parent.exists())
    
    def test_to_dict(self):
        """测试转换为字典"""
        config = Config()
        config.gemini_api_key = "test_key"
        
        config_dict = config.to_dict()
        
        # 验证字典结构
        self.assertIn('api', config_dict)
        self.assertIn('monitoring', config_dict)
        self.assertIn('processing', config_dict)
        self.assertIn('output', config_dict)
        self.assertIn('advanced', config_dict)
        
        # 验证具体值
        self.assertEqual(config_dict['api']['model_name'], config.model_name)
        self.assertEqual(config_dict['monitoring']['watch_directory'], config.watch_directory)
        self.assertEqual(config_dict['processing']['max_concurrent_files'], config.max_concurrent_files)
    
    @patch('config.load_dotenv')
    def test_load_with_dotenv(self, mock_load_dotenv):
        """测试使用dotenv加载配置"""
        with patch('config.Config.load_from_file') as mock_load_file, \
             patch('config.Config.load_from_env') as mock_load_env, \
             patch('config.Config.validate', return_value=True) as mock_validate:
            
            config = Config.load()
            
            # 验证调用顺序
            mock_load_dotenv.assert_called_once()
            mock_load_file.assert_called_once_with("data/config.ini")
            mock_load_env.assert_called_once()
            mock_validate.assert_called_once()
    
    def test_setup_logging(self):
        """测试日志设置"""
        config = Config()
        config.log_directory = str(Path(self.temp_dir) / "logs")
        config.log_level = "DEBUG"
        
        # 这个测试主要验证函数不会抛出异常
        try:
            setup_logging(config)
            success = True
        except Exception:
            success = False
        
        self.assertTrue(success)
        
        # 验证日志目录被创建
        self.assertTrue(Path(config.log_directory).exists())


class TestConfigIntegration(unittest.TestCase):
    """配置集成测试"""
    
    def test_full_config_load_cycle(self):
        """测试完整的配置加载周期"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试环境
            config_file = Path(temp_dir) / "config.ini"
            env_file = Path(temp_dir) / ".env"
            
            # 创建配置文件
            config_content = """
[API]
model_name = file_model

[MONITORING]
scan_interval_minutes = 15
"""
            with open(config_file, 'w') as f:
                f.write(config_content)
            
            # 设置环境变量（应该覆盖配置文件）
            test_env = {
                'GEMINI_API_KEY': 'env_api_key',
                'SCAN_INTERVAL_MINUTES': '45'
            }
            
            with patch.dict(os.environ, test_env):
                with patch('config.load_dotenv'):
                    config = Config.load(str(config_file))
                    
                    # 验证优先级：环境变量 > 配置文件 > 默认值
                    self.assertEqual(config.gemini_api_key, 'env_api_key')  # 来自环境变量
                    self.assertEqual(config.scan_interval_minutes, 45)  # 来自环境变量，覆盖配置文件
                    self.assertEqual(config.model_name, 'file_model')  # 来自配置文件
                    self.assertEqual(config.temperature, 0.3)  # 默认值


if __name__ == '__main__':
    unittest.main()
