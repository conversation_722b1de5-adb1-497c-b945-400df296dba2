#!/usr/bin/env python3
"""
修复版本的Gemini客户端测试
"""

import os
import re
import json
import time
import logging
from dotenv import load_dotenv
import google.generativeai as genai
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class TestSummaryResponse:
    """汇总响应数据类"""
    title: str
    core_facts: str
    key_points: List[str]
    importance_level: str
    importance_reason: str
    keywords: List[str]
    content_type: str
    processing_status: str
    error_message: Optional[str] = None

class TestGeminiClient:
    """测试版本的Gemini客户端"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model_name = model_name
        
        # 配置Gemini API
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
    
    def generate_summary(self, content: str, filename: str = "") -> TestSummaryResponse:
        """生成内容汇总"""
        try:
            # 生成提示词
            prompt = f"""
作为专业新闻汇总助手，请对以下新闻内容进行结构化汇总。

请按照以下JSON格式返回结果：
{{
    "title": "新闻标题",
    "core_facts": "核心事实",
    "key_points": ["要点1", "要点2", "要点3"],
    "importance_level": "高/中/低",
    "importance_reason": "重要性评估理由",
    "keywords": ["关键词1", "关键词2", "关键词3"]
}}

新闻内容：
{content}
"""
            
            # 调用API
            response = self.model.generate_content(prompt)
            response_text = response.text
            
            print(f"🔍 API原始响应: {repr(response_text[:200])}")
            
            # 解析响应
            summary = self._parse_response_fixed(response_text)
            return summary
            
        except Exception as e:
            print(f"❌ 生成汇总失败: {e}")
            return TestSummaryResponse(
                title="处理失败",
                core_facts="",
                key_points=[],
                importance_level="低",
                importance_reason="处理过程中发生错误",
                keywords=[],
                content_type="unknown",
                processing_status="error",
                error_message=str(e)
            )
    
    def _parse_response_fixed(self, response_text: str) -> TestSummaryResponse:
        """修复版本的响应解析"""
        try:
            print(f"🧹 开始解析响应，原始长度: {len(response_text)}")
            
            # 首先尝试去除markdown代码块标记
            cleaned_text = response_text.strip()
            
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]  # 去除```json
                print("✅ 去除```json开头")
                
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]  # 去除```
                print("✅ 去除```结尾")
                
            cleaned_text = cleaned_text.strip()
            print(f"🧹 清理后长度: {len(cleaned_text)}")
            print(f"🧹 清理后内容: {repr(cleaned_text[:100])}")
            
            # 尝试直接解析清理后的文本
            try:
                data = json.loads(cleaned_text)
                print("✅ 直接JSON解析成功")
            except Exception as e:
                print(f"❌ 直接JSON解析失败: {e}")
                # 如果直接解析失败，尝试提取JSON部分
                json_match = re.search(r'\\{.*\\}', response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    print(f"🔍 正则提取JSON: {repr(json_str[:100])}")
                    data = json.loads(json_str)
                    print("✅ 正则JSON解析成功")
                else:
                    raise ValueError("无法找到有效的JSON格式")
            
            # 构造返回结果
            result = TestSummaryResponse(
                title=data.get("title", "未知标题"),
                core_facts=data.get("core_facts", ""),
                key_points=data.get("key_points", []),
                importance_level=data.get("importance_level", "中"),
                importance_reason=data.get("importance_reason", ""),
                keywords=data.get("keywords", []),
                content_type="news",
                processing_status="success"
            )
            
            print(f"✅ 解析完成，标题: {result.title}")
            return result
            
        except Exception as e:
            print(f"❌ 解析失败: {type(e).__name__}: {e}")
            import traceback
            traceback.print_exc()
            
            return TestSummaryResponse(
                title="解析失败",
                core_facts="",
                key_points=[],
                importance_level="低",
                importance_reason="JSON解析失败",
                keywords=[],
                content_type="unknown",
                processing_status="error",
                error_message=str(e)
            )

def main():
    """测试主函数"""
    load_dotenv()
    
    client = TestGeminiClient(api_key=os.getenv("GEMINI_API_KEY"))
    
    test_content = "今日，中国国产x86 CPU首次应用于桌面AI PC，这是重要的技术突破。"
    
    print("🚀 测试修复版本的Gemini客户端")
    print("=" * 60)
    
    result = client.generate_summary(test_content, "测试.md")
    
    print("=" * 60)
    print("📊 测试结果:")
    print(f"处理状态: {result.processing_status}")
    print(f"标题: {result.title}")
    print(f"核心事实: {result.core_facts}")
    print(f"关键点: {result.key_points}")
    print(f"重要性: {result.importance_level}")
    print(f"关键词: {result.keywords}")
    
    if result.error_message:
        print(f"错误信息: {result.error_message}")

if __name__ == "__main__":
    main()