#!/usr/bin/env python3
"""
调试JSON解析问题
"""

import os
import re
import json
from dotenv import load_dotenv
import google.generativeai as genai

def test_direct_api_call():
    """直接调用API查看返回的原始数据"""
    load_dotenv()
    
    genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
    model = genai.GenerativeModel("gemini-2.0-flash-exp")
    
    test_content = """
    今日，中国国产x86 CPU首次应用于桌面AI PC，标志着在挑战Intel、AMD的道路上迈出重要一步。
    据悉，这款CPU在办公应用场景下表现良好。
    """
    
    prompt = f"""
作为专业新闻汇总助手，请对以下新闻内容进行结构化汇总。

请按照以下JSON格式返回结果：
{{
    "title": "新闻标题",
    "core_facts": "核心事实（包含5W1H：时间、地点、人物、事件、原因、影响）",
    "key_points": ["要点1", "要点2", "要点3"],
    "importance_level": "高/中/低",
    "importance_reason": "重要性评估理由",
    "keywords": ["关键词1", "关键词2", "关键词3"]
}}

汇总要求：
1. 提取核心事实，包含时间、地点、人物、事件、原因、影响等要素
2. 突出3-5个关键信息点
3. 评估新闻重要性等级（高/中/低）并说明理由
4. 生成3-5个相关关键词标签
5. 保持客观中立，避免主观判断

新闻内容：
{test_content}
"""
    
    print("🔍 发送到API的完整prompt:")
    print("=" * 60)
    print(prompt)
    print("=" * 60)
    
    try:
        response = model.generate_content(prompt)
        
        print("📄 API原始响应:")
        print("=" * 60)
        print(repr(response.text))  # 使用repr显示所有字符
        print("=" * 60)
        print("📄 API响应内容:")
        print(response.text)
        print("=" * 60)
        
        # 测试JSON解析
        print("🧪 测试JSON解析:")
        
        # 方法1：直接解析
        try:
            data = json.loads(response.text)
            print("✅ 直接JSON解析成功")
        except Exception as e:
            print(f"❌ 直接JSON解析失败: {e}")
        
        # 方法2：正则提取JSON
        try:
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print(f"正则提取的JSON: {repr(json_str)}")
                data = json.loads(json_str)
                print("✅ 正则JSON解析成功")
            else:
                print("❌ 正则无法找到JSON格式")
        except Exception as e:
            print(f"❌ 正则JSON解析失败: {e}")
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")

if __name__ == "__main__":
    test_direct_api_call()