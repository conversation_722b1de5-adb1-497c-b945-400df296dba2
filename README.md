# 新闻摘要程序 (News Summarizer)

一个基于Python的本地桌面应用，能够自动监控指定目录中的Markdown新闻文件，并使用Google Gemini 2.5 Flash API进行智能汇总。

## 功能特性

- 🔍 **自动文件监控**: 实时监控指定目录中的.md文件变化
- 🤖 **AI智能汇总**: 使用Google Gemini 2.5 Flash模型生成结构化摘要
- 📊 **多种内容类型**: 支持新闻、技术文章、普通文章的不同汇总策略
- 💾 **智能去重**: 避免重复处理相同文件，节省API调用成本
- 📈 **批量处理**: 支持大量文件的批量处理和增量更新
- 🛡️ **错误恢复**: 完善的错误处理和重试机制
- 📋 **多种输出**: JSON格式存储，支持Markdown报告生成
- 🧪 **完整测试**: 包含单元测试和集成测试
- 📊 **性能监控**: 内置性能监控和资源管理

## 快速开始

### 方法一：使用安装脚本（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd news_forge_ai_agent

# 运行安装脚本
chmod +x scripts/install.sh
./scripts/install.sh

# 编辑配置文件，设置API密钥
nano .env

# 运行程序
./run.sh
```

### 方法二：手动安装

#### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 2. 配置设置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置你的Gemini API密钥
# GEMINI_API_KEY=your_actual_api_key_here
```

#### 3. 运行应用

```bash
# 初始化配置和目录
python main.py --init

# 执行初始扫描
python main.py --scan

# 启动监控模式
python main.py
```

## 项目结构

```
news_forge_ai_agent/
├── main.py                 # 应用入口点
├── config.py              # 配置管理
├── requirements.txt       # 依赖声明
├── setup.py               # 安装脚本
├── README.md             # 使用文档
├── .env.example          # 环境变量模板
├── modules/              # 核心模块
│   ├── __init__.py       # 模块初始化
│   ├── file_monitor.py   # 文件监控
│   ├── gemini_client.py  # API客户端
│   ├── summarizer.py     # 汇总处理
│   ├── storage.py        # 数据存储
│   ├── error_handler.py  # 错误处理
│   └── utils.py          # 工具函数
├── data/                 # 数据目录
│   ├── config.ini        # 配置文件
│   ├── processed.json    # 处理记录
│   └── summaries/        # 汇总结果
├── logs/                 # 日志文件
├── tests/                # 测试文件
│   ├── __init__.py       # 测试模块初始化
│   ├── test_config.py    # 配置测试
│   ├── test_gemini_client.py  # API客户端测试
│   ├── test_storage.py   # 存储测试
│   └── run_tests.py      # 测试运行器
└── scripts/              # 脚本文件
    ├── install.sh        # 安装脚本
    └── run.sh           # 启动脚本
```

## 配置说明

### 环境变量配置

主要环境变量说明：

- `GEMINI_API_KEY`: Google Gemini API密钥（必需）
- `WATCH_DIRECTORY`: 监控的目录路径
- `SCAN_INTERVAL_MINUTES`: 扫描间隔（分钟）
- `MAX_CONCURRENT_FILES`: 最大并发处理文件数
- `LOG_LEVEL`: 日志级别（INFO/DEBUG/ERROR）

### 配置文件

详细配置可在 `data/config.ini` 中设置：

```ini
[API]
model_name = gemini-2.0-flash-exp
temperature = 0.3
max_tokens = 2048

[MONITORING]
watch_directory = /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox
scan_interval_minutes = 30
file_extensions = .md

[PROCESSING]
max_concurrent_files = 5
chunk_size_chars = 4000
retry_attempts = 3
```

## 使用示例

### 命令行选项

```bash
# 显示帮助信息
python main.py --help

# 手动扫描处理
python main.py --scan

# 查看处理状态
python main.py --status

# 生成汇总报告
python main.py --report markdown
python main.py --report json

# 设置日志级别
python main.py --log-level DEBUG

# 使用自定义配置文件
python main.py --config /path/to/config.ini
```

### 使用启动脚本

```bash
# 启动监控模式
./run.sh

# 手动扫描
./run.sh --scan

# 查看状态
./run.sh --status

# 生成报告
./run.sh --report
```

## 开发和测试

### 运行测试

```bash
# 运行所有测试
python tests/run_tests.py

# 运行特定模块测试
python tests/run_tests.py --module test_config

# 运行代码覆盖率分析
python tests/run_tests.py --coverage

# 生成测试报告
python tests/run_tests.py --report
```

### 开发环境设置

```bash
# 安装开发依赖
pip install -r requirements.txt

# 安装代码格式化工具
pip install black flake8 mypy

# 格式化代码
black .

# 代码检查
flake8 .
mypy .
```

## API使用说明

### 获取Gemini API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API密钥
3. 将密钥设置到环境变量 `GEMINI_API_KEY`

### API调用限制

- 免费版本有每分钟请求限制
- 建议设置合理的并发数和重试间隔
- 监控API使用量避免超出配额

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: GEMINI_API_KEY 未设置
   解决: 检查.env文件中的API密钥设置
   ```

2. **文件权限错误**
   ```
   错误: 文件访问权限被拒绝
   解决: 检查监控目录和输出目录的读写权限
   ```

3. **依赖包缺失**
   ```
   错误: ModuleNotFoundError
   解决: 运行 pip install -r requirements.txt
   ```

4. **内存不足**
   ```
   错误: 内存使用过高
   解决: 减少max_concurrent_files设置
   ```

### 日志查看

```bash
# 查看应用日志
tail -f logs/news_summarizer.log

# 查看错误日志
tail -f logs/errors.log

# 设置调试模式
export LOG_LEVEL=DEBUG
python main.py
```

## 性能优化

### 建议设置

- **并发处理**: 根据系统性能调整 `max_concurrent_files`
- **内存管理**: 启用 `memory_optimization` 选项
- **API限制**: 设置合理的 `api_rate_limit_per_minute`
- **文件过滤**: 使用 `ignore_hidden_files` 和 `ignore_temp_files`

### 监控指标

应用内置性能监控，可查看：
- CPU和内存使用率
- API响应时间
- 文件处理速度
- 错误率统计

## 许可证

MIT License

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的文件监控和汇总功能
- 完整的测试套件
- 详细的文档和安装脚本
