#!/usr/bin/env python3
"""
追踪错误的具体来源
"""

import os
import sys
import traceback
import logging
from dotenv import load_dotenv

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def trace_error():
    """详细追踪错误"""
    load_dotenv()
    
    try:
        # 使用我们最简单的客户端
        from modules.gemini_client_working import GeminiClient
        
        client = GeminiClient(api_key=os.getenv("GEMINI_API_KEY"))
        
        print("🔍 开始追踪...")
        
        # 逐步执行
        test_content = "这是一个测试内容"
        filename = "test.md"
        
        print("步骤1: 创建客户端完成")
        
        # 在generate_summary中添加详细追踪
        try:
            result = client.generate_summary(test_content, filename)
            print(f"步骤2: generate_summary完成，状态: {result.processing_status}")
            
            if result.processing_status == "error":
                print(f"错误详情: {result.error_message}")
                
        except Exception as e:
            print(f"步骤2失败: {e}")
            print("完整错误堆栈:")
            traceback.print_exc()
            
            # 查看异常的详细信息
            print(f"\n异常类型: {type(e)}")
            print(f"异常字符串: {repr(str(e))}")
            print(f"异常参数: {e.args}")
            
    except Exception as outer_e:
        print(f"外层异常: {outer_e}")
        traceback.print_exc()

if __name__ == "__main__":
    trace_error()