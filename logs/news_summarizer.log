2025-09-01 11:30:49,323 - __main__ - INFO - 新闻摘要程序启动
2025-09-01 11:30:49,323 - __main__ - INFO - 正在初始化应用组件...
2025-09-01 11:30:49,323 - modules.error_handler - INFO - 重试处理器已启动
2025-09-01 11:30:49,323 - __main__ - INFO - 应用组件初始化完成
2025-09-01 11:30:49,323 - modules.storage - INFO - 加载了 0 个汇总结果
2025-09-01 11:32:49,451 - __main__ - INFO - 新闻摘要程序启动
2025-09-01 11:32:49,451 - __main__ - INFO - 正在初始化应用组件...
2025-09-01 11:32:49,451 - modules.error_handler - INFO - 重试处理器已启动
2025-09-01 11:32:49,452 - __main__ - INFO - 应用组件初始化完成
2025-09-01 11:32:49,452 - modules.storage - INFO - 加载了 0 个汇总结果
2025-09-01 11:33:04,870 - __main__ - INFO - 新闻摘要程序启动
2025-09-01 11:33:04,870 - __main__ - INFO - 正在初始化应用组件...
2025-09-01 11:33:04,870 - modules.error_handler - INFO - 重试处理器已启动
2025-09-01 11:33:04,870 - __main__ - INFO - 应用组件初始化完成
2025-09-01 11:33:04,871 - __main__ - INFO - 开始手动扫描...
2025-09-01 11:33:04,877 - modules.summarizer - INFO - 开始批量处理 60 个文件
2025-09-01 11:33:04,877 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-01 11:33:04,879 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:04,879 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,879 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-01 11:33:04,883 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:04,883 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,884 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md, '\n    "title"'
2025-09-01 11:33:04,884 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_2509.json
2025-09-01 11:33:04,884 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-01 11:33:04,884 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-01 11:33:04,887 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,887 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,887 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md, '\n    "title"'
2025-09-01 11:33:04,887 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_1462.json
2025-09-01 11:33:04,887 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-01 11:33:04,893 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-01 11:33:04,894 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,894 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,890 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-01 11:33:04,890 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-01 11:33:04,885 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md, '\n    "title"'
2025-09-01 11:33:04,892 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-01 11:33:04,900 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_2179.json
2025-09-01 11:33:04,900 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-01 11:33:04,901 - modules.gemini_client - INFO - 检测到内容类型: unknown
2025-09-01 11:33:04,901 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:04,900 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md, '\n    "title"'
2025-09-01 11:33:04,908 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,908 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-01 11:33:04,909 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,909 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,910 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md, '\n    "title"'
2025-09-01 11:33:04,909 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,911 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,909 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_1960.json
2025-09-01 11:33:04,912 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-01 11:33:04,911 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_6180.json
2025-09-01 11:33:04,912 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,912 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-01 11:33:04,912 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-01 11:33:04,912 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md, '\n    "title"'
2025-09-01 11:33:04,925 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_8222.json
2025-09-01 11:33:04,925 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-01 11:33:04,924 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,913 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md, '\n    "title"'
2025-09-01 11:33:04,925 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-01 11:33:04,922 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md, '\n    "title"'
2025-09-01 11:33:04,926 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_3299.json
2025-09-01 11:33:04,926 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-01 11:33:04,926 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_3480.json
2025-09-01 11:33:04,932 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-01 11:33:04,925 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-01 11:33:04,931 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,926 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,933 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md, '\n    "title"'
2025-09-01 11:33:04,932 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-01 11:33:04,932 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,932 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-01 11:33:04,933 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_8448.json
2025-09-01 11:33:04,938 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,944 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,945 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md, '\n    "title"'
2025-09-01 11:33:04,945 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_4493.json
2025-09-01 11:33:04,945 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-01 11:33:04,942 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:04,944 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:04,945 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-01 11:33:04,946 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:04,946 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,943 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-01 11:33:04,943 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md, '\n    "title"'
2025-09-01 11:33:04,945 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,948 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-01 11:33:04,945 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,948 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_9875.json
2025-09-01 11:33:04,949 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md, '\n    "title"'
2025-09-01 11:33:04,949 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-01 11:33:04,950 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_9605.json
2025-09-01 11:33:04,950 - modules.gemini_client - INFO - 检测到内容类型: unknown
2025-09-01 11:33:04,951 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-01 11:33:04,951 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-01 11:33:04,951 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,951 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-01 11:33:04,952 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:04,952 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,952 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md, '\n    "title"'
2025-09-01 11:33:04,952 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_2762.json
2025-09-01 11:33:04,952 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-01 11:33:04,953 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:04,953 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-01 11:33:04,953 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,953 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md, '\n    "title"'
2025-09-01 11:33:04,954 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_3637.json
2025-09-01 11:33:04,954 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-01 11:33:04,955 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:04,955 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-01 11:33:04,955 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,955 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md, '\n    "title"'
2025-09-01 11:33:04,957 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,957 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md, '\n    "title"'
2025-09-01 11:33:04,957 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_0540.json
2025-09-01 11:33:04,957 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,957 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md, '\n    "title"'
2025-09-01 11:33:04,957 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_6228.json
2025-09-01 11:33:04,957 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-01 11:33:04,958 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_5484.json
2025-09-01 11:33:04,958 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-01 11:33:04,958 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-01 11:33:04,958 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-01 11:33:04,958 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-01 11:33:04,958 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md, '\n    "title"'
2025-09-01 11:33:04,958 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-01 11:33:04,958 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_5114.json
2025-09-01 11:33:04,958 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-01 11:33:04,958 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-01 11:33:04,959 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:04,965 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,965 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,962 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,969 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,965 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,969 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,965 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md, '\n    "title"'
2025-09-01 11:33:04,969 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,969 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_5724.json
2025-09-01 11:33:04,970 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-01 11:33:04,970 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-01 11:33:04,970 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md, '\n    "title"'
2025-09-01 11:33:04,970 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_8855.json
2025-09-01 11:33:04,970 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-01 11:33:04,972 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,972 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,972 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md, '\n    "title"'
2025-09-01 11:33:04,972 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_7558.json
2025-09-01 11:33:04,972 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-01 11:33:04,972 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-01 11:33:04,973 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md, '\n    "title"'
2025-09-01 11:33:04,973 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_2890.json
2025-09-01 11:33:04,973 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-01 11:33:04,973 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-01 11:33:04,974 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-01 11:33:04,974 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,974 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,975 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:04,976 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,997 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:04,997 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md, '\n    "title"'
2025-09-01 11:33:04,997 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md, '\n    "title"'
2025-09-01 11:33:04,997 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,997 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_5897.json
2025-09-01 11:33:04,997 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_0461.json
2025-09-01 11:33:04,997 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-01 11:33:04,997 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-01 11:33:04,997 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-01 11:33:04,997 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-01 11:33:04,998 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:04,998 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md, '\n    "title"'
2025-09-01 11:33:04,998 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,998 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_6475.json
2025-09-01 11:33:04,999 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md, '\n    "title"'
2025-09-01 11:33:04,999 - modules.gemini_client - INFO - 检测到内容类型: unknown
2025-09-01 11:33:04,999 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-01 11:33:04,999 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_9823.json
2025-09-01 11:33:04,999 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:04,999 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-01 11:33:04,999 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md, '\n    "title"'
2025-09-01 11:33:04,999 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-01 11:33:04,999 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-01 11:33:04,999 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_0749.json
2025-09-01 11:33:04,999 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-01 11:33:04,999 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-01 11:33:05,000 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,000 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,002 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,003 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,003 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md, '\n    "title"'
2025-09-01 11:33:05,003 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,003 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,003 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_3550.json
2025-09-01 11:33:05,003 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-01 11:33:05,003 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-01 11:33:05,003 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md, '\n    "title"'
2025-09-01 11:33:05,004 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113304_2070.json
2025-09-01 11:33:05,004 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-01 11:33:05,004 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-01 11:33:05,004 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:05,004 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,005 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md, '\n    "title"'
2025-09-01 11:33:05,005 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_5632.json
2025-09-01 11:33:05,005 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-01 11:33:05,007 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,007 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md, '\n    "title"'
2025-09-01 11:33:05,007 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-01 11:33:05,007 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,007 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_7785.json
2025-09-01 11:33:05,007 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md, '\n    "title"'
2025-09-01 11:33:05,007 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-01 11:33:05,007 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_5818.json
2025-09-01 11:33:05,007 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-01 11:33:05,007 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md, '\n    "title"'
2025-09-01 11:33:05,013 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-01 11:33:05,014 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,014 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_2541.json
2025-09-01 11:33:05,014 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-01 11:33:05,014 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,014 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-01 11:33:05,015 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-01 11:33:05,018 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,018 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:05,019 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,019 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,021 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,021 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,021 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md, '\n    "title"'
2025-09-01 11:33:05,021 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_0420.json
2025-09-01 11:33:05,021 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-01 11:33:05,021 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-01 11:33:05,023 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,023 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,023 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md, '\n    "title"'
2025-09-01 11:33:05,023 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_0216.json
2025-09-01 11:33:05,023 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-01 11:33:05,023 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-01 11:33:05,024 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md, '\n    "title"'
2025-09-01 11:33:05,024 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md, '\n    "title"'
2025-09-01 11:33:05,024 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_3139.json
2025-09-01 11:33:05,024 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_4815.json
2025-09-01 11:33:05,024 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-01 11:33:05,028 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,028 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md, '\n    "title"'
2025-09-01 11:33:05,028 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-01 11:33:05,028 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-01 11:33:05,028 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,028 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_6942.json
2025-09-01 11:33:05,028 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-01 11:33:05,028 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-01 11:33:05,032 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,032 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md, '\n    "title"'
2025-09-01 11:33:05,032 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-01 11:33:05,032 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,032 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_1829.json
2025-09-01 11:33:05,034 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,034 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-01 11:33:05,034 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,034 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-01 11:33:05,035 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,035 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,036 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,036 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,036 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md, '\n    "title"'
2025-09-01 11:33:05,036 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_2045.json
2025-09-01 11:33:05,036 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-01 11:33:05,037 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-01 11:33:05,037 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-01 11:33:05,037 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md, '\n    "title"'
2025-09-01 11:33:05,037 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_6952.json
2025-09-01 11:33:05,037 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md, '\n    "title"'
2025-09-01 11:33:05,037 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-01 11:33:05,037 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_5249.json
2025-09-01 11:33:05,037 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-01 11:33:05,040 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,040 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-01 11:33:05,040 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md, '\n    "title"'
2025-09-01 11:33:05,040 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,040 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-01 11:33:05,040 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_5215.json
2025-09-01 11:33:05,041 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md, '\n    "title"'
2025-09-01 11:33:05,041 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,041 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-01 11:33:05,041 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_2797.json
2025-09-01 11:33:05,041 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,041 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-01 11:33:05,041 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-01 11:33:05,044 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,044 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-01 11:33:05,044 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,046 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:05,046 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,046 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,046 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,047 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md, '\n    "title"'
2025-09-01 11:33:05,047 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_2865.json
2025-09-01 11:33:05,047 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-01 11:33:05,047 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-01 11:33:05,047 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md, '\n    "title"'
2025-09-01 11:33:05,047 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_5301.json
2025-09-01 11:33:05,047 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-01 11:33:05,047 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-01 11:33:05,049 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,049 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-01 11:33:05,049 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,049 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md, '\n    "title"'
2025-09-01 11:33:05,051 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,051 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_3002.json
2025-09-01 11:33:05,051 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md, '\n    "title"'
2025-09-01 11:33:05,051 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md, '\n    "title"'
2025-09-01 11:33:05,051 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,052 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-01 11:33:05,052 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_2663.json
2025-09-01 11:33:05,052 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_0369.json
2025-09-01 11:33:05,052 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-01 11:33:05,052 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-01 11:33:05,052 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-01 11:33:05,052 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-01 11:33:05,052 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-01 11:33:05,053 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-01 11:33:05,053 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md, '\n    "title"'
2025-09-01 11:33:05,053 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,053 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_3545.json
2025-09-01 11:33:05,055 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:05,055 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md, '\n    "title"'
2025-09-01 11:33:05,055 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-01 11:33:05,056 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:05,056 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,056 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_1938.json
2025-09-01 11:33:05,056 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-01 11:33:05,056 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,056 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-01 11:33:05,057 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md
2025-09-01 11:33:05,058 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,058 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,058 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md, '\n    "title"'
2025-09-01 11:33:05,058 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_9136.json
2025-09-01 11:33:05,058 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-01 11:33:05,059 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-01 11:33:05,059 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md
2025-09-01 11:33:05,059 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,059 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md, '\n    "title"'
2025-09-01 11:33:05,059 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_6148.json
2025-09-01 11:33:05,059 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-01 11:33:05,059 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md
2025-09-01 11:33:05,061 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,061 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,061 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md, '\n    "title"'
2025-09-01 11:33:05,062 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_4892.json
2025-09-01 11:33:05,062 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-01 11:33:05,065 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,065 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,065 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md
2025-09-01 11:33:05,065 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-01 11:33:05,065 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md, '\n    "title"'
2025-09-01 11:33:05,065 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_7072.json
2025-09-01 11:33:05,066 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,066 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-01 11:33:05,066 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,067 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md, '\n    "title"'
2025-09-01 11:33:05,067 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md
2025-09-01 11:33:05,067 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_0203.json
2025-09-01 11:33:05,067 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md
2025-09-01 11:33:05,069 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-01 11:33:05,069 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-01 11:33:05,070 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-01 11:33:05,070 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md, '\n    "title"'
2025-09-01 11:33:05,070 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-01 11:33:05,070 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_3069.json
2025-09-01 11:33:05,070 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md, '\n    "title"'
2025-09-01 11:33:05,070 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md
2025-09-01 11:33:05,070 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_2049.json
2025-09-01 11:33:05,070 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md, '\n    "title"'
2025-09-01 11:33:05,070 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md
2025-09-01 11:33:05,070 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_1353.json
2025-09-01 11:33:05,070 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md
2025-09-01 11:33:05,070 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md, '\n    "title"'
2025-09-01 11:33:05,070 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250901_113305_9485.json
2025-09-01 11:33:05,070 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md
2025-09-01 11:33:05,071 - modules.summarizer - INFO - 批量处理完成: 成功 0, 失败 60, 跳过 0
2025-09-01 11:33:33,278 - __main__ - INFO - 新闻摘要程序启动
2025-09-01 11:33:33,279 - __main__ - INFO - 正在初始化应用组件...
2025-09-01 11:33:33,281 - modules.error_handler - INFO - 重试处理器已启动
2025-09-01 11:33:33,281 - modules.storage - INFO - 加载了 60 个已处理文件记录
2025-09-01 11:33:33,281 - __main__ - INFO - 应用组件初始化完成
2025-09-01 11:33:33,281 - __main__ - INFO - 开始手动扫描...
2025-09-01 20:47:40,904 - __main__ - INFO - 新闻摘要程序启动
2025-09-01 20:47:40,904 - __main__ - INFO - 正在初始化应用组件...
2025-09-01 20:47:40,904 - modules.error_handler - INFO - 重试处理器已启动
2025-09-01 20:47:40,905 - modules.storage - INFO - 加载了 60 个已处理文件记录
2025-09-01 20:47:40,905 - __main__ - INFO - 应用组件初始化完成
2025-09-01 20:47:40,911 - modules.storage - INFO - 加载了 50 个汇总结果
2025-09-02 09:25:05,831 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:25:05,831 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:25:05,832 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:25:05,832 - modules.storage - INFO - 加载了 60 个已处理文件记录
2025-09-02 09:25:05,832 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:25:05,832 - __main__ - INFO - 开始手动扫描...
2025-09-02 09:25:56,177 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:25:56,177 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:25:56,177 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:25:56,178 - modules.storage - INFO - 加载了 60 个已处理文件记录
2025-09-02 09:25:56,178 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:25:56,178 - __main__ - INFO - 开始手动扫描...
2025-09-02 09:25:56,189 - modules.summarizer - INFO - 开始批量处理 1 个文件
2025-09-02 09:25:56,189 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 09:25:56,191 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:25:56,191 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:25:56,191 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md, '\n    "title"'
2025-09-02 09:25:56,191 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_092556_6621.json
2025-09-02 09:25:56,191 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 09:25:56,192 - modules.summarizer - INFO - 批量处理完成: 成功 0, 失败 1, 跳过 0
2025-09-02 09:33:02,146 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:33:02,147 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:33:02,147 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:33:02,147 - modules.storage - INFO - 加载了 0 个已处理文件记录
2025-09-02 09:33:02,147 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:33:02,147 - __main__ - INFO - 开始手动扫描...
2025-09-02 09:33:02,150 - modules.summarizer - INFO - 开始批量处理 61 个文件
2025-09-02 09:33:02,150 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 09:33:02,150 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 09:33:02,150 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 09:33:02,150 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 09:33:02,151 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 09:33:02,156 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,159 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,159 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,160 - modules.gemini_client - INFO - 检测到内容类型: unknown
2025-09-02 09:33:02,160 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,161 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,161 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,161 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,161 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,161 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,162 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md, '\n    "title"'
2025-09-02 09:33:02,162 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_3973.json
2025-09-02 09:33:02,162 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md, '\n    "title"'
2025-09-02 09:33:02,163 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 09:33:02,163 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_3151.json
2025-09-02 09:33:02,163 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 09:33:02,163 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 09:33:02,163 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md, '\n    "title"'
2025-09-02 09:33:02,163 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 09:33:02,163 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_9796.json
2025-09-02 09:33:02,163 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 09:33:02,163 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md, '\n    "title"'
2025-09-02 09:33:02,163 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 09:33:02,163 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_8725.json
2025-09-02 09:33:02,164 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,164 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md, '\n    "title"'
2025-09-02 09:33:02,165 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,165 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 09:33:02,165 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,165 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_6635.json
2025-09-02 09:33:02,165 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,165 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 09:33:02,165 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 09:33:02,165 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 09:33:02,166 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,171 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,171 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,173 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,173 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,173 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,174 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md, '\n    "title"'
2025-09-02 09:33:02,174 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_9824.json
2025-09-02 09:33:02,174 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 09:33:02,174 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md, '\n    "title"'
2025-09-02 09:33:02,174 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_6338.json
2025-09-02 09:33:02,174 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 09:33:02,174 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 09:33:02,174 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md, '\n    "title"'
2025-09-02 09:33:02,174 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 09:33:02,174 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_3474.json
2025-09-02 09:33:02,174 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 09:33:02,175 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 09:33:02,176 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,176 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md, '\n    "title"'
2025-09-02 09:33:02,177 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,177 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,177 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md, '\n    "title"'
2025-09-02 09:33:02,177 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2414.json
2025-09-02 09:33:02,177 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,177 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7460.json
2025-09-02 09:33:02,177 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 09:33:02,177 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,178 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 09:33:02,178 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 09:33:02,178 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,178 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 09:33:02,178 - modules.gemini_client - INFO - 检测到内容类型: unknown
2025-09-02 09:33:02,178 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,179 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,179 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,179 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md, '\n    "title"'
2025-09-02 09:33:02,179 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_8356.json
2025-09-02 09:33:02,179 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 09:33:02,179 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md, '\n    "title"'
2025-09-02 09:33:02,180 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 09:33:02,180 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2388.json
2025-09-02 09:33:02,180 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 09:33:02,180 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md, '\n    "title"'
2025-09-02 09:33:02,180 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 09:33:02,180 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7216.json
2025-09-02 09:33:02,180 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 09:33:02,180 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 09:33:02,180 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md, '\n    "title"'
2025-09-02 09:33:02,180 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_6183.json
2025-09-02 09:33:02,181 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,181 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 09:33:02,181 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md, '\n    "title"'
2025-09-02 09:33:02,181 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,182 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,182 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 09:33:02,182 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_0340.json
2025-09-02 09:33:02,182 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,182 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 09:33:02,183 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,184 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,184 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 09:33:02,185 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,185 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,188 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,188 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,188 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md, '\n    "title"'
2025-09-02 09:33:02,188 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_9100.json
2025-09-02 09:33:02,188 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 09:33:02,188 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 09:33:02,188 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md, '\n    "title"'
2025-09-02 09:33:02,188 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_4796.json
2025-09-02 09:33:02,188 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 09:33:02,189 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 09:33:02,190 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,190 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,190 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,190 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,190 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,190 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md, '\n    "title"'
2025-09-02 09:33:02,190 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md, '\n    "title"'
2025-09-02 09:33:02,190 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_3738.json
2025-09-02 09:33:02,190 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_8762.json
2025-09-02 09:33:02,190 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 09:33:02,190 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 09:33:02,190 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 09:33:02,190 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md, '\n    "title"'
2025-09-02 09:33:02,190 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 09:33:02,191 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2421.json
2025-09-02 09:33:02,193 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,197 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,197 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,197 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 09:33:02,197 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,197 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,197 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md, '\n    "title"'
2025-09-02 09:33:02,197 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md, '\n    "title"'
2025-09-02 09:33:02,197 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 09:33:02,197 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_5436.json
2025-09-02 09:33:02,197 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_0062.json
2025-09-02 09:33:02,197 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 09:33:02,197 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 09:33:02,197 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 09:33:02,197 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 09:33:02,199 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,199 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,200 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,201 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,201 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,202 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,202 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md, '\n    "title"'
2025-09-02 09:33:02,202 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_6985.json
2025-09-02 09:33:02,202 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 09:33:02,202 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 09:33:02,202 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md, '\n    "title"'
2025-09-02 09:33:02,202 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_8171.json
2025-09-02 09:33:02,202 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 09:33:02,202 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 09:33:02,202 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md, '\n    "title"'
2025-09-02 09:33:02,202 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_1554.json
2025-09-02 09:33:02,203 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md, '\n    "title"'
2025-09-02 09:33:02,203 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 09:33:02,203 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_1855.json
2025-09-02 09:33:02,203 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md, '\n    "title"'
2025-09-02 09:33:02,203 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,209 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 09:33:02,215 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 09:33:02,225 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,225 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7586.json
2025-09-02 09:33:02,225 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,226 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 09:33:02,226 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,226 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 09:33:02,226 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 09:33:02,227 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,227 - modules.gemini_client - INFO - 检测到内容类型: unknown
2025-09-02 09:33:02,227 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,228 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,228 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,228 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,229 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md, '\n    "title"'
2025-09-02 09:33:02,229 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_8518.json
2025-09-02 09:33:02,229 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 09:33:02,229 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md, '\n    "title"'
2025-09-02 09:33:02,229 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 09:33:02,229 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2024.json
2025-09-02 09:33:02,229 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 09:33:02,229 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 09:33:02,231 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,232 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,232 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,232 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,232 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md, '\n    "title"'
2025-09-02 09:33:02,232 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,232 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md, '\n    "title"'
2025-09-02 09:33:02,232 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_8679.json
2025-09-02 09:33:02,232 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md, '\n    "title"'
2025-09-02 09:33:02,232 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2878.json
2025-09-02 09:33:02,232 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 09:33:02,232 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_6480.json
2025-09-02 09:33:02,233 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 09:33:02,233 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 09:33:02,233 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 09:33:02,233 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 09:33:02,233 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 09:33:02,240 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,241 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,244 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,246 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,246 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md, '\n    "title"'
2025-09-02 09:33:02,246 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md, '\n    "title"'
2025-09-02 09:33:02,246 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,246 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,246 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_4607.json
2025-09-02 09:33:02,246 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_3118.json
2025-09-02 09:33:02,246 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 09:33:02,246 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 09:33:02,246 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 09:33:02,246 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 09:33:02,247 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,247 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,249 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,249 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,250 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,250 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md, '\n    "title"'
2025-09-02 09:33:02,250 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_4341.json
2025-09-02 09:33:02,250 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md, '\n    "title"'
2025-09-02 09:33:02,250 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md, '\n    "title"'
2025-09-02 09:33:02,250 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 09:33:02,250 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2866.json
2025-09-02 09:33:02,251 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_0869.json
2025-09-02 09:33:02,251 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 09:33:02,251 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md, '\n    "title"'
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 09:33:02,251 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md, '\n    "title"'
2025-09-02 09:33:02,251 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_0507.json
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 09:33:02,251 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2862.json
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 09:33:02,251 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 09:33:02,253 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,257 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,257 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,261 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,261 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,262 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,262 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,262 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,263 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,263 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,264 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md, '\n    "title"'
2025-09-02 09:33:02,264 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2944.json
2025-09-02 09:33:02,264 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md, '\n    "title"'
2025-09-02 09:33:02,264 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 09:33:02,265 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md, '\n    "title"'
2025-09-02 09:33:02,265 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2765.json
2025-09-02 09:33:02,265 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 09:33:02,265 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_1295.json
2025-09-02 09:33:02,265 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 09:33:02,265 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md, '\n    "title"'
2025-09-02 09:33:02,265 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 09:33:02,265 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md, '\n    "title"'
2025-09-02 09:33:02,265 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 09:33:02,265 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_5407.json
2025-09-02 09:33:02,265 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 09:33:02,265 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_1163.json
2025-09-02 09:33:02,265 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 09:33:02,266 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,266 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 09:33:02,269 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,269 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 09:33:02,270 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,270 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,270 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 09:33:02,270 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,270 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,273 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,273 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,275 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,275 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,275 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,275 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md, '\n    "title"'
2025-09-02 09:33:02,275 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2155.json
2025-09-02 09:33:02,275 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 09:33:02,276 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 09:33:02,276 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md, '\n    "title"'
2025-09-02 09:33:02,276 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md, '\n    "title"'
2025-09-02 09:33:02,276 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_8706.json
2025-09-02 09:33:02,276 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_3033.json
2025-09-02 09:33:02,276 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 09:33:02,276 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,277 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 09:33:02,277 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,277 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 09:33:02,277 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,277 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 09:33:02,277 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md, '\n    "title"'
2025-09-02 09:33:02,277 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md, '\n    "title"'
2025-09-02 09:33:02,277 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_2448.json
2025-09-02 09:33:02,277 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_3274.json
2025-09-02 09:33:02,277 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 09:33:02,279 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,279 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 09:33:02,280 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,281 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-02 09:33:02,281 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,281 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-02 09:33:02,281 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,281 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md, '\n    "title"'
2025-09-02 09:33:02,281 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_5296.json
2025-09-02 09:33:02,281 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 09:33:02,281 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-02 09:33:02,282 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,283 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,284 - modules.gemini_client - INFO - 检测到内容类型: news
2025-09-02 09:33:02,285 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,286 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md, '\n    "title"'
2025-09-02 09:33:02,286 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,286 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,286 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,286 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_0753.json
2025-09-02 09:33:02,286 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md, '\n    "title"'
2025-09-02 09:33:02,286 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 09:33:02,286 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7956.json
2025-09-02 09:33:02,286 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-02 09:33:02,286 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 09:33:02,286 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md
2025-09-02 09:33:02,288 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,288 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,288 - modules.gemini_client - INFO - 检测到内容类型: general_article
2025-09-02 09:33:02,288 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md, '\n    "title"'
2025-09-02 09:33:02,288 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md, '\n    "title"'
2025-09-02 09:33:02,288 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,288 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7058.json
2025-09-02 09:33:02,288 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md, '\n    "title"'
2025-09-02 09:33:02,288 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_0994.json
2025-09-02 09:33:02,289 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-02 09:33:02,289 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7316.json
2025-09-02 09:33:02,289 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-02 09:33:02,289 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md
2025-09-02 09:33:02,289 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-02 09:33:02,289 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md
2025-09-02 09:33:02,289 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md
2025-09-02 09:33:02,291 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,291 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md, '\n    "title"'
2025-09-02 09:33:02,291 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,292 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_5072.json
2025-09-02 09:33:02,292 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md, '\n    "title"'
2025-09-02 09:33:02,292 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-02 09:33:02,292 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_4543.json
2025-09-02 09:33:02,292 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md
2025-09-02 09:33:02,292 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md
2025-09-02 09:33:02,292 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md, '\n    "title"'
2025-09-02 09:33:02,292 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7686.json
2025-09-02 09:33:02,292 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md
2025-09-02 09:33:02,293 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,297 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,297 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,299 - modules.gemini_client - INFO - 检测到内容类型: tech_article
2025-09-02 09:33:02,299 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,299 - modules.gemini_client - ERROR - 生成汇总失败: '\n    "title"'
2025-09-02 09:33:02,300 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:33:02,300 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md, '\n    "title"'
2025-09-02 09:33:02,300 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_7742.json
2025-09-02 09:33:02,300 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md
2025-09-02 09:33:02,300 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md, '\n    "title"'
2025-09-02 09:33:02,300 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md, '\n    "title"'
2025-09-02 09:33:02,300 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_6314.json
2025-09-02 09:33:02,300 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093302_5690.json
2025-09-02 09:33:02,300 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md
2025-09-02 09:33:02,300 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md
2025-09-02 09:33:02,301 - modules.summarizer - INFO - 批量处理完成: 成功 0, 失败 61, 跳过 0
2025-09-02 09:39:15,782 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:39:15,783 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:39:15,784 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:39:15,784 - modules.storage - INFO - 加载了 0 个已处理文件记录
2025-09-02 09:39:15,784 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:39:15,784 - __main__ - INFO - 开始手动扫描...
2025-09-02 09:39:15,786 - modules.summarizer - INFO - 开始批量处理 61 个文件
2025-09-02 09:39:15,786 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 09:39:15,787 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 09:39:15,787 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 09:39:15,787 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 09:39:15,787 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 09:39:18,682 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093918_1660.json
2025-09-02 09:39:18,682 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 09:39:18,683 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 09:39:18,912 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093918_4349.json
2025-09-02 09:39:18,912 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 09:39:18,912 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 09:39:18,952 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093918_4840.json
2025-09-02 09:39:18,952 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 09:39:18,952 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 09:39:19,022 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093919_7044.json
2025-09-02 09:39:19,022 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 09:39:19,022 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 09:39:19,342 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093919_8720.json
2025-09-02 09:39:19,342 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 09:39:19,342 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 09:39:19,607 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 40
}
]
2025-09-02 09:39:19,608 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 40
}
]
2025-09-02 09:39:19,609 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093919_3315.json
2025-09-02 09:39:19,609 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 09:39:19,609 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 09:39:20,576 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093920_9172.json
2025-09-02 09:39:20,577 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093920_4789.json
2025-09-02 09:39:20,577 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 09:39:20,577 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 09:39:20,577 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 09:39:20,577 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 09:39:20,654 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093920_4551.json
2025-09-02 09:39:20,654 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 09:39:20,654 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 09:39:20,835 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093920_8700.json
2025-09-02 09:39:20,835 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 09:39:20,835 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 09:39:21,126 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,127 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,127 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093921_3204.json
2025-09-02 09:39:21,127 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 09:39:21,127 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 09:39:21,455 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093921_9485.json
2025-09-02 09:39:21,455 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 09:39:21,455 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 09:39:21,631 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,632 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,632 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093921_2618.json
2025-09-02 09:39:21,632 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 09:39:21,632 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 09:39:21,813 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,814 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,815 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093921_1685.json
2025-09-02 09:39:21,815 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 09:39:21,815 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 09:39:21,992 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,993 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 38
}
]
2025-09-02 09:39:21,994 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093921_8237.json
2025-09-02 09:39:21,994 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 09:39:21,994 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 09:39:22,115 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,117 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,117 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_2016.json
2025-09-02 09:39:22,117 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 09:39:22,117 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 09:39:22,174 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_3381.json
2025-09-02 09:39:22,174 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 09:39:22,174 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 09:39:22,263 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_4673.json
2025-09-02 09:39:22,263 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 09:39:22,263 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 09:39:22,353 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,354 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_7429.json
2025-09-02 09:39:22,354 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 09:39:22,354 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 09:39:22,355 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,355 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_3352.json
2025-09-02 09:39:22,355 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 09:39:22,355 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 09:39:22,532 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,532 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,532 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,534 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:39:22,534 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,535 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:39:22,535 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_9459.json
2025-09-02 09:39:22,535 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,535 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 09:39:22,535 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,535 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_4195.json
2025-09-02 09:39:22,535 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 09:39:22,535 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_9823.json
2025-09-02 09:39:22,535 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 09:39:22,535 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 09:39:22,535 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 09:39:22,535 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 09:39:22,710 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,710 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,712 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,712 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_2348.json
2025-09-02 09:39:22,712 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,712 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 09:39:22,712 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_0044.json
2025-09-02 09:39:22,713 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 09:39:22,713 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 09:39:22,713 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 09:39:22,892 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,893 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,893 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,896 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,896 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,896 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_7649.json
2025-09-02 09:39:22,896 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_2660.json
2025-09-02 09:39:22,896 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 09:39:22,896 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:22,896 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 09:39:22,896 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 09:39:22,896 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093922_7586.json
2025-09-02 09:39:22,896 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 09:39:22,896 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 09:39:22,897 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 09:39:23,072 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:23,073 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:23,075 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:23,075 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
]
2025-09-02 09:39:23,075 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_6104.json
2025-09-02 09:39:23,075 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_3846.json
2025-09-02 09:39:23,075 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 09:39:23,075 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 09:39:23,075 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 09:39:23,076 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 09:39:23,142 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,144 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,144 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_3726.json
2025-09-02 09:39:23,144 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 09:39:23,144 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 09:39:23,251 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,252 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,253 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,253 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_3025.json
2025-09-02 09:39:23,253 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 09:39:23,253 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 09:39:23,253 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,253 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_0858.json
2025-09-02 09:39:23,253 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 09:39:23,253 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 09:39:23,432 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,433 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,433 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,436 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:39:23,436 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,436 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:39:23,436 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_4632.json
2025-09-02 09:39:23,436 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,437 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 09:39:23,437 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_0387.json
2025-09-02 09:39:23,437 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 09:39:23,437 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,437 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 09:39:23,437 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_0332.json
2025-09-02 09:39:23,437 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 09:39:23,437 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 09:39:23,437 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 09:39:23,612 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,612 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,617 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,617 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,617 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_0795.json
2025-09-02 09:39:23,617 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_4511.json
2025-09-02 09:39:23,617 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 09:39:23,617 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 09:39:23,617 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 09:39:23,618 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 09:39:23,792 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,792 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,795 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,796 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,796 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_7819.json
2025-09-02 09:39:23,796 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_5451.json
2025-09-02 09:39:23,796 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 09:39:23,796 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 09:39:23,796 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 09:39:23,796 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 09:39:23,972 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,972 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,972 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,974 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:39:23,974 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,974 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,974 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_1292.json
2025-09-02 09:39:23,974 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_1666.json
2025-09-02 09:39:23,974 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 09:39:23,974 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 09:39:23,974 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 36
}
]
2025-09-02 09:39:23,974 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 09:39:23,974 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 09:39:23,975 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093923_8697.json
2025-09-02 09:39:23,975 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 09:39:23,975 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 09:39:24,043 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,044 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,044 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_3036.json
2025-09-02 09:39:24,044 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 09:39:24,044 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 09:39:24,152 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,154 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,154 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_2387.json
2025-09-02 09:39:24,155 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 09:39:24,155 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 09:39:24,333 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,333 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,335 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,335 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_6015.json
2025-09-02 09:39:24,335 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 09:39:24,335 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,335 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 09:39:24,335 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_4936.json
2025-09-02 09:39:24,335 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 09:39:24,335 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 09:39:24,512 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,512 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,513 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,517 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,517 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,517 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,517 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_3369.json
2025-09-02 09:39:24,517 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_0879.json
2025-09-02 09:39:24,517 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_2811.json
2025-09-02 09:39:24,517 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 09:39:24,517 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 09:39:24,517 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 09:39:24,517 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-02 09:39:24,518 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-02 09:39:24,518 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-02 09:39:24,692 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,695 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,695 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_3104.json
2025-09-02 09:39:24,695 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 09:39:24,695 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-02 09:39:24,873 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,873 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,873 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,874 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,879 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,879 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,879 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,879 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_0178.json
2025-09-02 09:39:24,879 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:24,879 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_2144.json
2025-09-02 09:39:24,879 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_7844.json
2025-09-02 09:39:24,879 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 09:39:24,879 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093924_0227.json
2025-09-02 09:39:24,879 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-02 09:39:24,879 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-02 09:39:24,879 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md
2025-09-02 09:39:24,879 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-02 09:39:24,880 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md
2025-09-02 09:39:24,880 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md
2025-09-02 09:39:24,880 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md
2025-09-02 09:39:25,052 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:25,055 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
]
2025-09-02 09:39:25,055 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093925_3179.json
2025-09-02 09:39:25,055 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-02 09:39:25,055 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md
2025-09-02 09:39:25,233 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,233 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,234 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,234 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,238 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:39:25,238 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,239 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093925_6181.json
2025-09-02 09:39:25,239 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/深圳小伙卖地下管道机器人：年入2.49亿，港股上市，全球唯一-36氪-2025-08-31 06-57-56.md
2025-09-02 09:39:25,240 - modules.storage - ERROR - 保存处理记录失败: dictionary changed size during iteration
2025-09-02 09:39:25,240 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,240 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093925_0599.json
2025-09-02 09:39:25,240 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30 06-31-10.md
2025-09-02 09:39:25,242 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,242 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093925_4484.json
2025-09-02 09:39:25,242 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/“世界最轻量网站”阿部宽官网将https化 网友：老电脑测速神器要消失了--快科技--科技改变未来-2025-08-30.md
2025-09-02 09:39:25,242 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,243 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093925_4957.json
2025-09-02 09:39:25,243 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/ByteDance推出AetherCode：揭秘AI编程大赛中的真实差距有多大_手机新浪网-2025-08-31 06-41-47.md
2025-09-02 09:39:25,411 - modules.gemini_client - ERROR - 生成汇总失败: 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,413 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md, 429 You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-09-02 09:39:25,413 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_093925_6031.json
2025-09-02 09:39:25,413 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华裔女学霸揭秘Claude Code，一人带六个AI开干！编程范式彻底被颠覆_手机新浪网-2025-08-31 06-42-33.md
2025-09-02 09:39:25,413 - modules.summarizer - INFO - 批量处理完成: 成功 13, 失败 48, 跳过 0
2025-09-02 09:51:43,449 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:51:43,449 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:51:43,449 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:51:43,449 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 09:51:43,449 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 09:51:43,450 - modules.storage - INFO - 加载了 61 个已处理文件记录
2025-09-02 09:51:43,450 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:51:43,450 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 09:51:49,281 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:51:49,282 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:51:49,282 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:51:49,282 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 09:51:49,282 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 09:51:49,282 - modules.storage - INFO - 加载了 61 个已处理文件记录
2025-09-02 09:51:49,282 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:51:49,283 - __main__ - INFO - 开始手动扫描...
2025-09-02 09:51:53,670 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:51:53,670 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:51:53,671 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:51:53,671 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 09:51:53,671 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 09:51:53,672 - modules.storage - INFO - 加载了 61 个已处理文件记录
2025-09-02 09:51:53,672 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:51:53,672 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 09:55:44,520 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:55:44,520 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:55:44,521 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:55:44,521 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 09:55:44,521 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 09:55:44,521 - modules.storage - INFO - 加载了 0 个已处理文件记录
2025-09-02 09:55:44,521 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:55:44,521 - __main__ - INFO - 开始手动扫描...
2025-09-02 09:55:44,523 - modules.summarizer - INFO - 开始批量处理 61 个文件
2025-09-02 09:55:44,523 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 09:55:44,523 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 09:55:44,524 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 09:55:44,524 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 09:55:44,528 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 09:55:44,529 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 09:55:44,531 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 09:55:44,536 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 09:55:44,536 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 09:55:44,536 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 09:55:44,540 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 09:55:44,540 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 09:55:44,541 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 09:55:44,541 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 09:55:44,541 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 09:55:57,405 - modules.gemini_client - INFO - 成功处理 中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md，耗时: 12.87秒，模型: gemini-2.5-pro
2025-09-02 09:55:57,407 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095557_9699.json
2025-09-02 09:55:57,407 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 09:55:57,407 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 09:55:57,410 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 09:55:57,410 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 09:55:57,867 - modules.gemini_client - INFO - 成功处理 人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md，耗时: 13.33秒，模型: gemini-2.5-pro
2025-09-02 09:55:57,869 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095557_6751.json
2025-09-02 09:55:57,869 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 09:55:57,869 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 09:55:57,872 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 09:55:57,872 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 09:55:59,744 - modules.gemini_client - INFO - 成功处理 NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md，耗时: 15.20秒，模型: gemini-2.5-pro
2025-09-02 09:55:59,745 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095559_5282.json
2025-09-02 09:55:59,745 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 09:55:59,745 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 09:55:59,746 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 09:55:59,746 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 09:56:01,535 - modules.gemini_client - INFO - 成功处理 智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md，耗时: 16.99秒，模型: gemini-2.5-pro
2025-09-02 09:56:01,536 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095601_3394.json
2025-09-02 09:56:01,536 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 09:56:01,536 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 09:56:01,541 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 09:56:01,541 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 09:56:02,276 - modules.gemini_client - INFO - 成功处理 突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md，耗时: 17.73秒，模型: gemini-2.5-pro
2025-09-02 09:56:02,277 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095602_0396.json
2025-09-02 09:56:02,277 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 09:56:02,277 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 09:56:02,285 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 0.00%)
2025-09-02 09:56:02,285 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 09:56:07,735 - modules.gemini_client - INFO - 成功处理 华尔街见闻-2025-08-31 06-43-27.md，耗时: 9.86秒，模型: gemini-2.5-pro
2025-09-02 09:56:07,738 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095607_2688.json
2025-09-02 09:56:07,738 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 09:56:07,738 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 09:56:07,742 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 0.00%)
2025-09-02 09:56:07,742 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 09:56:08,096 - modules.gemini_client - INFO - 成功处理 6G，取得新突破！-2025-08-31 06-54-47.md，耗时: 8.35秒，模型: gemini-2.5-pro
2025-09-02 09:56:08,099 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095608_8211.json
2025-09-02 09:56:08,099 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 09:56:08,100 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 09:56:08,103 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 0.00%)
2025-09-02 09:56:08,103 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 09:56:09,135 - modules.gemini_client - INFO - 成功处理 黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md，耗时: 11.72秒，模型: gemini-2.5-pro
2025-09-02 09:56:09,136 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095609_0320.json
2025-09-02 09:56:09,136 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 09:56:09,136 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 09:56:09,137 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 0.00%)
2025-09-02 09:56:09,137 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 09:56:10,824 - modules.gemini_client - INFO - 成功处理 华尔街见闻-2025-08-31 06-42-18.md，耗时: 8.54秒，模型: gemini-2.5-flash
2025-09-02 09:56:10,825 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095610_4132.json
2025-09-02 09:56:10,825 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 09:56:10,825 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 09:56:10,826 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 09:56:10,827 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 09:56:11,185 - modules.gemini_client - INFO - 成功处理 破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md，耗时: 9.64秒，模型: gemini-2.5-pro
2025-09-02 09:56:11,188 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_095611_9577.json
2025-09-02 09:56:11,189 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 09:56:11,189 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 09:56:11,190 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 09:56:11,190 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 09:56:23,758 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 09:56:23,759 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 09:56:23,759 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 09:56:23,759 - modules.rate_limiter - INFO - 已加载速率限制历史记录: 3 个模型
2025-09-02 09:56:23,759 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 09:56:23,759 - modules.storage - INFO - 加载了 10 个已处理文件记录
2025-09-02 09:56:23,760 - __main__ - INFO - 应用组件初始化完成
2025-09-02 09:56:23,760 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:11,719 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 10:01:11,720 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 10:01:11,720 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 10:01:11,720 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 10:01:11,720 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 10:01:11,721 - modules.storage - INFO - 加载了 0 个已处理文件记录
2025-09-02 10:01:11,721 - __main__ - INFO - 应用组件初始化完成
2025-09-02 10:01:11,721 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 10:01:16,126 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 10:01:16,126 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 10:01:16,126 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 10:01:16,126 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 10:01:16,126 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 10:01:16,127 - modules.storage - INFO - 加载了 0 个已处理文件记录
2025-09-02 10:01:16,127 - __main__ - INFO - 应用组件初始化完成
2025-09-02 10:01:16,127 - __main__ - INFO - 开始手动扫描...
2025-09-02 10:01:16,132 - modules.summarizer - INFO - 开始批量处理 61 个文件
2025-09-02 10:01:16,133 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 10:01:16,133 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 10:01:16,133 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 10:01:16,133 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 10:01:16,134 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 10:01:16,144 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 10:01:16,144 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 10:01:16,148 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 10:01:16,149 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 10:01:16,149 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 10:01:16,149 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 10:01:16,149 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 10:01:16,149 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 0.00%)
2025-09-02 10:01:16,150 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 10:01:16,150 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 10:01:27,540 - modules.gemini_client - INFO - 成功处理 智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md，耗时: 11.39秒，模型: gemini-2.5-pro
2025-09-02 10:01:27,543 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100127_0658.json
2025-09-02 10:01:27,543 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 10:01:27,543 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 10:01:27,546 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:01:27,546 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 10:01:27,630 - modules.gemini_client - INFO - 成功处理 NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md，耗时: 11.48秒，模型: gemini-2.5-pro
2025-09-02 10:01:27,631 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100127_0700.json
2025-09-02 10:01:27,631 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 10:01:27,631 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 10:01:27,633 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:01:27,633 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 10:01:28,919 - modules.gemini_client - INFO - 成功处理 突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md，耗时: 12.77秒，模型: gemini-2.5-pro
2025-09-02 10:01:28,922 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100128_2794.json
2025-09-02 10:01:28,922 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 10:01:28,922 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 10:01:28,925 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:01:28,925 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 10:01:29,780 - modules.gemini_client - INFO - 成功处理 人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md，耗时: 13.63秒，模型: gemini-2.5-pro
2025-09-02 10:01:29,783 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100129_9323.json
2025-09-02 10:01:29,784 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 10:01:29,784 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 10:01:29,792 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:01:29,792 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 10:01:29,910 - modules.gemini_client - INFO - 成功处理 中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md，耗时: 13.76秒，模型: gemini-2.5-pro
2025-09-02 10:01:29,911 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100129_5740.json
2025-09-02 10:01:29,911 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 10:01:29,912 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 10:01:29,922 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 0.00%)
2025-09-02 10:01:29,922 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 10:01:37,628 - modules.gemini_client - INFO - 成功处理 6G，取得新突破！-2025-08-31 06-54-47.md，耗时: 8.70秒，模型: gemini-2.5-pro
2025-09-02 10:01:37,629 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100137_8913.json
2025-09-02 10:01:37,629 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 10:01:37,629 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 10:01:37,634 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 0.00%)
2025-09-02 10:01:37,634 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 10:01:38,749 - modules.gemini_client - INFO - 成功处理 黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md，耗时: 11.20秒，模型: gemini-2.5-pro
2025-09-02 10:01:38,752 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100138_5643.json
2025-09-02 10:01:38,752 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 10:01:38,753 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 10:01:38,756 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 0.00%)
2025-09-02 10:01:38,756 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 10:01:38,809 - modules.gemini_client - INFO - 成功处理 华尔街见闻-2025-08-31 06-42-18.md，耗时: 8.89秒，模型: gemini-2.5-flash
2025-09-02 10:01:38,811 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100138_1265.json
2025-09-02 10:01:38,811 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 10:01:38,811 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 10:01:38,814 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:38,814 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 10:01:39,901 - modules.gemini_client - INFO - 成功处理 华尔街见闻-2025-08-31 06-43-27.md，耗时: 12.27秒，模型: gemini-2.5-pro
2025-09-02 10:01:39,904 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100139_1272.json
2025-09-02 10:01:39,904 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 10:01:39,904 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 10:01:39,907 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:39,907 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 10:01:41,287 - modules.gemini_client - INFO - 成功处理 破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md，耗时: 11.50秒，模型: gemini-2.5-pro
2025-09-02 10:01:41,288 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100141_8293.json
2025-09-02 10:01:41,288 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 10:01:41,288 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 10:01:41,289 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:41,289 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 10:01:46,829 - modules.gemini_client - INFO - 成功处理 国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md，耗时: 8.01秒，模型: gemini-2.5-flash
2025-09-02 10:01:46,831 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100146_3529.json
2025-09-02 10:01:46,831 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 10:01:46,831 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 10:01:46,836 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:46,836 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 10:01:47,438 - modules.gemini_client - INFO - 成功处理 华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md，耗时: 7.53秒，模型: gemini-2.5-flash
2025-09-02 10:01:47,439 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100147_3285.json
2025-09-02 10:01:47,439 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 10:01:47,439 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 10:01:47,440 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:47,440 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 10:01:48,469 - modules.gemini_client - INFO - 成功处理 有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md，耗时: 10.83秒，模型: gemini-2.5-flash
2025-09-02 10:01:48,471 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100148_6272.json
2025-09-02 10:01:48,471 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 10:01:48,471 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 10:01:48,472 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:48,473 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 10:01:48,847 - modules.gemini_client - INFO - 成功处理 国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md，耗时: 10.09秒，模型: gemini-2.5-flash
2025-09-02 10:01:48,848 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100148_4456.json
2025-09-02 10:01:48,848 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 10:01:48,848 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 10:01:48,851 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:48,851 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 10:01:50,369 - modules.gemini_client - INFO - 成功处理 普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md，耗时: 9.08秒，模型: gemini-2.5-flash
2025-09-02 10:01:50,371 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100150_6203.json
2025-09-02 10:01:50,371 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 10:01:50,371 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 10:01:50,380 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:50,380 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 10:01:56,369 - modules.gemini_client - INFO - 成功处理 19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md，耗时: 5.99秒，模型: gemini-2.5-flash
2025-09-02 10:01:56,369 - modules.gemini_client - INFO - 成功处理 AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md，耗时: 8.93秒，模型: gemini-2.5-flash
2025-09-02 10:01:56,373 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100156_9920.json
2025-09-02 10:01:56,373 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 10:01:56,373 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100156_5506.json
2025-09-02 10:01:56,373 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 10:01:56,373 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 10:01:56,373 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 10:01:56,377 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:56,378 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:56,378 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 10:01:56,378 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: test_news.md
2025-09-02 10:01:56,558 - modules.gemini_client - INFO - 成功处理 中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md，耗时: 9.72秒，模型: gemini-2.5-flash
2025-09-02 10:01:56,560 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100156_4978.json
2025-09-02 10:01:56,560 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 10:01:56,560 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 10:01:56,565 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 2, 成功率: 100.00%)
2025-09-02 10:01:56,565 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 10:01:57,678 - modules.gemini_client - INFO - 成功处理 为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md，耗时: 9.21秒，模型: gemini-2.5-flash
2025-09-02 10:01:57,680 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100157_2288.json
2025-09-02 10:01:57,680 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 10:01:57,680 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 10:01:57,687 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 0.00%)
2025-09-02 10:01:57,687 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 10:01:59,138 - modules.gemini_client - INFO - 成功处理 Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md，耗时: 10.29秒，模型: gemini-2.5-flash
2025-09-02 10:01:59,139 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100159_9468.json
2025-09-02 10:01:59,139 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 10:01:59,139 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 10:01:59,142 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 0.00%)
2025-09-02 10:01:59,142 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 10:02:00,178 - modules.gemini_client - INFO - 成功处理 吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md，耗时: 2.49秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:00,180 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100200_9439.json
2025-09-02 10:02:00,180 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 10:02:00,180 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 10:02:00,183 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:00,183 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 10:02:01,579 - modules.gemini_client - INFO - 成功处理 MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md，耗时: 2.44秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:01,582 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100201_9214.json
2025-09-02 10:02:01,582 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 10:02:01,582 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 10:02:01,587 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:01,587 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 10:02:02,458 - modules.gemini_client - INFO - 成功处理 神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md，耗时: 2.27秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:02,459 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100202_8707.json
2025-09-02 10:02:02,459 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 10:02:02,459 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 10:02:02,492 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:02,492 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 10:02:04,094 - modules.gemini_client - INFO - 成功处理 爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md，耗时: 1.60秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:04,095 - modules.gemini_client - INFO - 成功处理 携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md，耗时: 2.51秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:04,099 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100204_1907.json
2025-09-02 10:02:04,103 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 10:02:04,103 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 10:02:04,104 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100204_4569.json
2025-09-02 10:02:04,104 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 10:02:04,104 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 10:02:04,106 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:04,107 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 10:02:04,107 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:04,107 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 10:02:06,478 - modules.gemini_client - INFO - 成功处理 有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md，耗时: 2.37秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:06,479 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100206_8124.json
2025-09-02 10:02:06,479 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 10:02:06,479 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 10:02:06,480 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:06,481 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 10:02:06,997 - modules.gemini_client - INFO - 成功处理 Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md，耗时: 2.89秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:06,998 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100206_0596.json
2025-09-02 10:02:06,998 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 10:02:06,998 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 10:02:06,999 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:07,000 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 10:02:07,189 - modules.gemini_client - INFO - 成功处理 晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md，耗时: 10.81秒，模型: gemini-2.5-flash
2025-09-02 10:02:07,190 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100207_6579.json
2025-09-02 10:02:07,190 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 10:02:07,190 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 10:02:07,193 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:07,193 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 10:02:08,099 - modules.gemini_client - INFO - 成功处理 美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md，耗时: 11.53秒，模型: gemini-2.5-flash
2025-09-02 10:02:08,103 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100208_5894.json
2025-09-02 10:02:08,103 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 10:02:08,103 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 10:02:08,107 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:08,107 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 10:02:09,100 - modules.gemini_client - INFO - 成功处理 微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md，耗时: 2.10秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:09,103 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100209_4588.json
2025-09-02 10:02:09,103 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 10:02:09,104 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 10:02:09,108 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:09,109 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 10:02:09,508 - modules.gemini_client - INFO - 成功处理 test_news.md，耗时: 13.13秒，模型: gemini-2.5-flash
2025-09-02 10:02:09,508 - modules.gemini_client - INFO - 成功处理 政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md，耗时: 2.31秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:09,511 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100209_0006.json
2025-09-02 10:02:09,511 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 10:02:09,511 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100209_7926.json
2025-09-02 10:02:09,511 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 10:02:09,511 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 10:02:09,512 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 10:02:09,533 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:09,533 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:09,533 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 10:02:09,533 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 10:02:09,587 - modules.gemini_client - INFO - 成功处理 新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md，耗时: 3.11秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:09,588 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100209_5347.json
2025-09-02 10:02:09,588 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 10:02:09,588 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 10:02:09,590 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:09,590 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 10:02:10,529 - modules.gemini_client - INFO - 成功处理 新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md，耗时: 2.42秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:10,531 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100210_3014.json
2025-09-02 10:02:10,531 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 10:02:10,531 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 10:02:10,536 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:10,536 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 10:02:11,567 - modules.gemini_client - INFO - 成功处理 单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md，耗时: 1.98秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:11,567 - modules.gemini_client - INFO - 成功处理 发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md，耗时: 2.46秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:11,568 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100211_0942.json
2025-09-02 10:02:11,569 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100211_4738.json
2025-09-02 10:02:11,569 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 10:02:11,569 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 10:02:11,569 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 10:02:11,569 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 10:02:11,576 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:11,576 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:11,576 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 10:02:11,576 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 10:02:11,578 - modules.gemini_client - INFO - 成功处理 Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md，耗时: 2.04秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:11,579 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100211_9380.json
2025-09-02 10:02:11,579 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 10:02:11,579 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 10:02:11,585 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:02:11,585 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 10:02:12,568 - modules.gemini_client - INFO - 成功处理 Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md，耗时: 3.03秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:12,569 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100212_5448.json
2025-09-02 10:02:12,569 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 10:02:12,570 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 10:02:12,573 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:02:12,573 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 15.0 秒
2025-09-02 10:02:12,758 - modules.gemini_client - INFO - 成功处理 大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md，耗时: 2.22秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:12,760 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100212_7796.json
2025-09-02 10:02:12,760 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 10:02:12,760 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 10:02:12,762 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:02:12,762 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 14.8 秒
2025-09-02 10:02:13,850 - modules.gemini_client - INFO - 成功处理 川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md，耗时: 2.27秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:13,851 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100213_0642.json
2025-09-02 10:02:13,851 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 10:02:13,852 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 10:02:13,854 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:02:13,854 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 13.7 秒
2025-09-02 10:02:15,778 - modules.gemini_client - INFO - 成功处理 Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md，耗时: 4.19秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:15,781 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100215_7587.json
2025-09-02 10:02:15,781 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 10:02:15,781 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 10:02:15,788 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:02:15,788 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 11.8 秒
2025-09-02 10:02:15,899 - modules.gemini_client - INFO - 成功处理 AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md，耗时: 4.32秒，模型: gemini-2.5-flash-lite
2025-09-02 10:02:15,900 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100215_9179.json
2025-09-02 10:02:15,900 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 10:02:15,900 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 10:02:15,901 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:02:15,901 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 11.6 秒
2025-09-02 10:02:27,540 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:02:27,540 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:02:27,540 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:02:27,540 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:02:27,540 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:02:27,540 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 11.3 秒
2025-09-02 10:02:27,541 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 11.3 秒
2025-09-02 10:02:27,541 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 11.3 秒
2025-09-02 10:02:27,541 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 11.3 秒
2025-09-02 10:02:27,541 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 11.3 秒
2025-09-02 10:02:38,809 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:02:38,809 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:02:38,809 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:02:38,809 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:02:38,809 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:02:38,810 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 21.4 秒
2025-09-02 10:02:38,810 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 21.4 秒
2025-09-02 10:02:38,810 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 21.4 秒
2025-09-02 10:02:38,810 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 21.4 秒
2025-09-02 10:02:38,810 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 21.4 秒
2025-09-02 10:03:00,179 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (18/15)
2025-09-02 10:03:00,179 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (18/15)
2025-09-02 10:03:00,179 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (18/15)
2025-09-02 10:03:00,179 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (18/15)
2025-09-02 10:03:00,179 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (18/15)
2025-09-02 10:03:00,185 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md, 所有模型都已达到速率限制
2025-09-02 10:03:00,185 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md, 所有模型都已达到速率限制
2025-09-02 10:03:00,185 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md, 所有模型都已达到速率限制
2025-09-02 10:03:00,185 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100300_2536.json
2025-09-02 10:03:00,185 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100300_4247.json
2025-09-02 10:03:00,185 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md, 所有模型都已达到速率限制
2025-09-02 10:03:00,185 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100300_9681.json
2025-09-02 10:03:00,185 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 10:03:00,186 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100300_8950.json
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 10:03:00,186 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md, 所有模型都已达到速率限制
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 10:03:00,186 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100300_9135.json
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 10:03:00,186 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 10:03:00,192 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 10:03:00,197 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:00,198 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:00,198 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 10:03:00,198 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:00,198 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:00,198 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 10:03:00,201 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:00,202 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 10:03:00,202 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 10:03:00,202 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 10:03:12,149 - modules.gemini_client - INFO - 成功处理 具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md，耗时: 11.95秒，模型: gemini-2.5-pro
2025-09-02 10:03:12,152 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100312_9654.json
2025-09-02 10:03:12,153 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 10:03:12,153 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-02 10:03:12,157 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:12,157 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 从老城烟火到文化地标 “介就是天津”！-_中国青年网-2025-08-31 06-39-52.md
2025-09-02 10:03:14,427 - modules.gemini_client - INFO - 成功处理 为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md，耗时: 14.23秒，模型: gemini-2.5-pro
2025-09-02 10:03:14,429 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100314_3285.json
2025-09-02 10:03:14,430 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 10:03:14,430 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-02 10:03:14,431 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:14,431 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 马来西亚顶尖机器人科学家移居中国，“这里有独特优势，他国难以企及”-2025-08-31 00-02-21.md
2025-09-02 10:03:14,720 - modules.gemini_client - INFO - 成功处理 小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md，耗时: 14.52秒，模型: gemini-2.5-pro
2025-09-02 10:03:14,722 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100314_9297.json
2025-09-02 10:03:14,723 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 10:03:14,723 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-02 10:03:14,726 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:14,726 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: AI信息加标识，合成内容应“亮明身份”！-新华网-2025-08-30 23-59-13.md
2025-09-02 10:03:14,827 - modules.gemini_client - INFO - 成功处理 高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md，耗时: 14.63秒，模型: gemini-2.5-pro
2025-09-02 10:03:14,828 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100314_4635.json
2025-09-02 10:03:14,828 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 10:03:14,828 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-02 10:03:14,830 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:03:14,830 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 播客在中国为什么做不太起来-2025-08-30 07-36-20.md
2025-09-02 10:03:30,519 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 10:03:30,519 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 10:03:30,520 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 10:03:30,520 - modules.rate_limiter - INFO - 已加载速率限制历史记录: 3 个模型
2025-09-02 10:03:30,520 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 10:03:30,521 - modules.storage - INFO - 加载了 51 个已处理文件记录
2025-09-02 10:03:30,521 - __main__ - INFO - 应用组件初始化完成
2025-09-02 10:03:30,521 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 1, 成功率: 100.00%)
2025-09-02 10:05:13,274 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 10:05:13,274 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 10:05:13,274 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 10:05:13,274 - modules.rate_limiter - INFO - 已加载速率限制历史记录: 3 个模型
2025-09-02 10:05:13,274 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 10:05:13,275 - modules.storage - INFO - 加载了 51 个已处理文件记录
2025-09-02 10:05:13,275 - __main__ - INFO - 应用组件初始化完成
2025-09-02 10:05:13,275 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:05:56,085 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 10:05:56,087 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 10:05:56,088 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 10:05:56,088 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 10:05:56,088 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 10:05:56,088 - modules.storage - INFO - 加载了 0 个已处理文件记录
2025-09-02 10:05:56,088 - __main__ - INFO - 应用组件初始化完成
2025-09-02 10:05:56,088 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 0.00%)
2025-09-02 10:06:01,683 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 10:06:01,683 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 10:06:01,684 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 10:06:01,685 - modules.rate_limiter - INFO - 速率限制记录文件不存在，将创建新文件
2025-09-02 10:06:01,685 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 10:06:01,685 - modules.storage - INFO - 加载了 0 个已处理文件记录
2025-09-02 10:06:01,685 - __main__ - INFO - 应用组件初始化完成
2025-09-02 10:06:01,685 - __main__ - INFO - 开始手动扫描...
2025-09-02 10:06:01,689 - modules.summarizer - INFO - 开始批量处理 61 个文件
2025-09-02 10:06:01,689 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 10:06:01,689 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 10:06:01,689 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 10:06:01,693 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 0.00%)
2025-09-02 10:06:01,693 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 10:06:01,698 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 10:06:01,698 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 0.00%)
2025-09-02 10:06:01,698 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 10:06:01,701 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 0.00%)
2025-09-02 10:06:01,701 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 10:06:01,701 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 10:06:01,710 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 0.00%)
2025-09-02 10:06:01,710 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 10:06:01,713 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 0.00%)
2025-09-02 10:06:01,714 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 10:06:10,323 - modules.gemini_client - INFO - 成功处理 NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md，耗时: 8.61秒，模型: gemini-2.5-flash
2025-09-02 10:06:10,326 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100610_4445.json
2025-09-02 10:06:10,326 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/NVIDIA Jetson Thor 为通用机器人和物理 AI 解锁实时推理能力 - NVIDIA 英伟达博客-2025-08-30.md
2025-09-02 10:06:10,326 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 10:06:10,329 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:10,330 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 10:06:10,509 - modules.gemini_client - INFO - 成功处理 智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md，耗时: 8.82秒，模型: gemini-2.5-flash
2025-09-02 10:06:10,510 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100610_8753.json
2025-09-02 10:06:10,510 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/智能门锁线上均价跌破900元：千元以下占七成销量--快科技--科技改变未来-2025-08-31 06-53-36.md
2025-09-02 10:06:10,510 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 10:06:10,511 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:10,511 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 10:06:11,639 - modules.gemini_client - INFO - 成功处理 中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md，耗时: 9.94秒，模型: gemini-2.5-flash
2025-09-02 10:06:11,641 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100611_7675.json
2025-09-02 10:06:11,641 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国国产x86 CPU首度应用于桌面AI PC：挑战Intel、AMD  办公没问题--快科技--科技改变未来-2025-08-31 00-03-23.md
2025-09-02 10:06:11,641 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 10:06:11,642 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:11,642 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 10:06:12,470 - modules.gemini_client - INFO - 成功处理 人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md，耗时: 10.77秒，模型: gemini-2.5-flash
2025-09-02 10:06:12,472 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100612_1413.json
2025-09-02 10:06:12,472 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/人形机器人带火视触觉传感器赛道，资本涌入，多家公司密集宣布融资-36氪-2025-08-31 06-58-41.md
2025-09-02 10:06:12,472 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 10:06:12,482 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:12,482 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 10:06:13,558 - modules.gemini_client - INFO - 成功处理 突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md，耗时: 11.85秒，模型: gemini-2.5-flash
2025-09-02 10:06:13,559 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100613_5280.json
2025-09-02 10:06:13,559 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/突破SAM局限！美团提出X-SAM：统一框架横扫20+分割基准-2025-08-31 19-01-33.md
2025-09-02 10:06:13,559 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 10:06:13,567 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:13,567 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 10:06:15,748 - modules.gemini_client - INFO - 成功处理 黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md，耗时: 5.42秒，模型: gemini-2.5-flash
2025-09-02 10:06:15,749 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100615_0248.json
2025-09-02 10:06:15,749 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：所有产品全部卖光了！--快科技--科技改变未来-2025-08-31 00-03-33.md
2025-09-02 10:06:15,749 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 10:06:15,751 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:15,751 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 10:06:19,747 - modules.gemini_client - INFO - 成功处理 华尔街见闻-2025-08-31 06-43-27.md，耗时: 9.24秒，模型: gemini-2.5-flash
2025-09-02 10:06:19,749 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100619_0607.json
2025-09-02 10:06:19,749 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-43-27.md
2025-09-02 10:06:19,749 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 10:06:19,750 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:19,750 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 10:06:19,938 - modules.gemini_client - INFO - 成功处理 6G，取得新突破！-2025-08-31 06-54-47.md，耗时: 8.30秒，模型: gemini-2.5-flash
2025-09-02 10:06:19,942 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100619_2404.json
2025-09-02 10:06:19,942 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/6G，取得新突破！-2025-08-31 06-54-47.md
2025-09-02 10:06:19,942 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 10:06:19,943 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:19,943 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 10:06:22,309 - modules.gemini_client - INFO - 成功处理 华尔街见闻-2025-08-31 06-42-18.md，耗时: 8.74秒，模型: gemini-2.5-flash
2025-09-02 10:06:22,311 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100622_0939.json
2025-09-02 10:06:22,311 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华尔街见闻-2025-08-31 06-42-18.md
2025-09-02 10:06:22,311 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 10:06:22,314 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:06:22,314 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 10:06:22,478 - modules.gemini_client - INFO - 成功处理 破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md，耗时: 10.00秒，模型: gemini-2.5-flash
2025-09-02 10:06:22,480 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100622_0317.json
2025-09-02 10:06:22,481 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/破解AI百万小时级视觉记忆“密码”：95后华人创业开发首个大视觉记忆模型，像人类一样“看见”并“记忆”-2025-08-31 19-03-06.md
2025-09-02 10:06:22,481 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 10:06:22,482 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 0.00%)
2025-09-02 10:06:22,482 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 10:06:24,160 - modules.gemini_client - INFO - 成功处理 有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md，耗时: 8.41秒，模型: gemini-2.5-flash
2025-09-02 10:06:24,161 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100624_8866.json
2025-09-02 10:06:24,161 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有补贴、有补助！记得领→_政策解读_中国政府网-2025-08-31 00-01-05.md
2025-09-02 10:06:24,161 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 10:06:24,164 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 0.00%)
2025-09-02 10:06:24,164 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 10:06:28,158 - modules.gemini_client - INFO - 成功处理 国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md，耗时: 8.41秒，模型: gemini-2.5-flash
2025-09-02 10:06:28,160 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100628_0145.json
2025-09-02 10:06:28,160 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国家发改委：发展“人工智能+”坚决避免无序竞争和一拥而上-2025-08-31 17-12-29.md
2025-09-02 10:06:28,160 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 10:06:28,162 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 0.00%)
2025-09-02 10:06:28,162 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 10:06:33,418 - modules.gemini_client - INFO - 成功处理 华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md，耗时: 11.10秒，模型: gemini-2.5-flash
2025-09-02 10:06:33,419 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100633_6714.json
2025-09-02 10:06:33,419 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/华为任正非、DeepSeek 梁文锋、宇树王兴兴入选《时代》TIME100 AI 2025 全球百大人物“领导者”榜单 - IT之家-2025-08-31 00-00-36.md
2025-09-02 10:06:33,419 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 10:06:33,420 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 0.00%)
2025-09-02 10:06:33,420 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 10:06:34,280 - modules.gemini_client - INFO - 成功处理 普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md，耗时: 11.80秒，模型: gemini-2.5-pro
2025-09-02 10:06:34,283 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100634_0451.json
2025-09-02 10:06:34,283 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/普通人拿不到大结果，关键并不在于能力-虎嗅网-2025-08-30.md
2025-09-02 10:06:34,283 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 10:06:34,290 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 100.00%)
2025-09-02 10:06:34,290 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 10:06:35,190 - modules.gemini_client - INFO - 成功处理 国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md，耗时: 15.25秒，模型: gemini-2.5-flash
2025-09-02 10:06:35,192 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100635_5591.json
2025-09-02 10:06:35,192 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/国务院重磅AI新政发布，未来10年最大机会在哪里？-虎嗅网-2025-08-30.md
2025-09-02 10:06:35,192 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 10:06:35,199 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 100.00%)
2025-09-02 10:06:35,199 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 10:06:37,608 - modules.gemini_client - INFO - 成功处理 中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md，耗时: 13.44秒，模型: gemini-2.5-pro
2025-09-02 10:06:37,609 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100637_9662.json
2025-09-02 10:06:37,609 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/中国为产能过剩的太阳能产品找到新买家：非洲-2025-08-30 06-48-16.md
2025-09-02 10:06:37,609 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 10:06:37,611 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 100.00%)
2025-09-02 10:06:37,611 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 10:06:38,920 - modules.gemini_client - INFO - 成功处理 AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md，耗时: 10.76秒，模型: gemini-2.5-pro
2025-09-02 10:06:38,923 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100638_2380.json
2025-09-02 10:06:38,923 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI大会论文被国人刷榜 华为人不感觉意外：通信领域已出现过--快科技--科技改变未来-2025-08-31 06-45-54.md
2025-09-02 10:06:38,923 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 10:06:38,924 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 100.00%)
2025-09-02 10:06:38,925 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: test_news.md
2025-09-02 10:06:44,748 - modules.gemini_client - INFO - 成功处理 为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md，耗时: 11.33秒，模型: gemini-2.5-pro
2025-09-02 10:06:44,749 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100644_9257.json
2025-09-02 10:06:44,749 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为啥定25而不是普遍认可的35-40？这就是明摆着让你难受，骑不快，又不好用_风闻-2025-08-31 06-44-57.md
2025-09-02 10:06:44,749 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 10:06:44,755 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-pro (优先级: 2, 成功率: 100.00%)
2025-09-02 10:06:44,755 - modules.gemini_client - INFO - 使用模型 gemini-2.5-pro 处理文件: 美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 10:06:52,418 - modules.gemini_client - INFO - 成功处理 晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md，耗时: 14.81秒，模型: gemini-2.5-pro
2025-09-02 10:06:52,421 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100652_4381.json
2025-09-02 10:06:52,421 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/晶采观察丨2分钟带你读懂“人工智能+”行动-_中国青年网-2025-08-31 17-12-54.md
2025-09-02 10:06:52,421 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 10:06:52,428 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 0.00%)
2025-09-02 10:06:52,428 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 10:06:52,618 - modules.gemini_client - INFO - 成功处理 test_news.md，耗时: 13.69秒，模型: gemini-2.5-pro
2025-09-02 10:06:52,620 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100652_2678.json
2025-09-02 10:06:52,620 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/test_news.md
2025-09-02 10:06:52,620 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 10:06:52,623 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 0.00%)
2025-09-02 10:06:52,623 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 10:06:55,178 - modules.gemini_client - INFO - 成功处理 吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md，耗时: 2.75秒，模型: gemini-2.5-flash-lite
2025-09-02 10:06:55,178 - modules.gemini_client - INFO - 成功处理 MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md，耗时: 2.55秒，模型: gemini-2.5-flash-lite
2025-09-02 10:06:55,180 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100655_5165.json
2025-09-02 10:06:55,180 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/吴恩达谈“氛围编程”：别被名字误导，AI编程并不轻松-36氪-2025-08-31 06-44-06.md
2025-09-02 10:06:55,180 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 10:06:55,180 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100655_5312.json
2025-09-02 10:06:55,180 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/MOVA割草机器人卖出10万台背后，一场进入下半场的残酷战争｜Insight全球-36氪-2025-08-31 06-58-21.md
2025-09-02 10:06:55,180 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 10:06:55,183 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:55,183 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:55,183 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 10:06:55,183 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 10:06:57,040 - modules.gemini_client - INFO - 成功处理 19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md，耗时: 21.84秒，模型: gemini-2.5-pro
2025-09-02 10:06:57,041 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100657_1897.json
2025-09-02 10:06:57,042 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/19块9的新型“付费恋人”，正在横扫七夕？-虎嗅网-2025-08-31 06-56-00.md
2025-09-02 10:06:57,042 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 10:06:57,076 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:57,076 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 10:06:57,678 - modules.gemini_client - INFO - 成功处理 美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md，耗时: 12.92秒，模型: gemini-2.5-pro
2025-09-02 10:06:57,680 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100657_2578.json
2025-09-02 10:06:57,680 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/美国版“人工智能+”，当前正面临怎样的主要矛盾？-36氪-2025-08-30 07-28-21.md
2025-09-02 10:06:57,680 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 10:06:57,681 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:57,681 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 10:06:57,748 - modules.gemini_client - INFO - 成功处理 神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md，耗时: 2.56秒，模型: gemini-2.5-flash-lite
2025-09-02 10:06:57,749 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100657_4736.json
2025-09-02 10:06:57,749 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/神奇的手性超导：超导与磁性可以共存？_风闻-2025-08-31 06-41-30.md
2025-09-02 10:06:57,749 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 10:06:57,752 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:57,752 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 10:06:57,828 - modules.gemini_client - INFO - 成功处理 携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md，耗时: 2.64秒，模型: gemini-2.5-flash-lite
2025-09-02 10:06:57,829 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100657_8455.json
2025-09-02 10:06:57,829 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/携手构建更加紧密的上合组织命运共同体--_光明网-2025-08-31 06-39-37.md
2025-09-02 10:06:57,829 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 10:06:57,830 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:57,830 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 10:06:58,888 - modules.gemini_client - INFO - 成功处理 Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md，耗时: 24.60秒，模型: gemini-2.5-pro
2025-09-02 10:06:58,890 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100658_3346.json
2025-09-02 10:06:58,890 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Coinbase也逃不过996 - BlockBeats-2025-08-31 06-54-15.md
2025-09-02 10:06:58,890 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 10:06:58,892 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:58,893 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 10:06:59,090 - modules.gemini_client - INFO - 成功处理 爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md，耗时: 2.01秒，模型: gemini-2.5-flash-lite
2025-09-02 10:06:59,102 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100659_2944.json
2025-09-02 10:06:59,102 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/爬虫代理ip-网页抓取-搜索引擎结果数据采集-IPIPGO-2025-08-31 10-03-14.md
2025-09-02 10:06:59,102 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 10:06:59,109 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:59,111 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 10:06:59,838 - modules.gemini_client - INFO - 成功处理 Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md，耗时: 2.16秒，模型: gemini-2.5-flash-lite
2025-09-02 10:06:59,839 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100659_8738.json
2025-09-02 10:06:59,839 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic重磅升级：Claude Code企业版正式上线，命令行编程工具迎来变革时刻 - 来上云吧，企业上云一站式服务-2025-08-31 06-42-49.md
2025-09-02 10:06:59,839 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 10:06:59,840 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:06:59,840 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 10:07:00,258 - modules.gemini_client - INFO - 成功处理 有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md，耗时: 2.51秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:00,259 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100700_8908.json
2025-09-02 10:07:00,259 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/有多少医疗科普自媒体号，“做出了违背祖宗的决定”？-虎嗅网-2025-08-30.md
2025-09-02 10:07:00,259 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 10:07:00,262 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:00,262 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 10:07:00,460 - modules.gemini_client - INFO - 成功处理 新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md，耗时: 2.63秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:00,461 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100700_5962.json
2025-09-02 10:07:00,461 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新一代逆超线程？英特尔 SDC“超级核心”专利公布：多个小核齐心协力提升单线程性能 - IT之家-2025-08-31 06-46-34.md
2025-09-02 10:07:00,462 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 10:07:00,473 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:00,473 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 10:07:00,659 - modules.gemini_client - INFO - 成功处理 微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md，耗时: 1.77秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:00,662 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100700_8413.json
2025-09-02 10:07:00,662 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/微软 DocumentDB 宣布加入 Linux 基金会，推动开源文档数据库发展 - IT之家-2025-08-31 17-19-33.md
2025-09-02 10:07:00,662 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 10:07:00,672 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:00,673 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 10:07:01,770 - modules.gemini_client - INFO - 成功处理 新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md，耗时: 1.93秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:01,773 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100701_2791.json
2025-09-02 10:07:01,773 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/新华鲜报-重大部署！中国“人工智能+”行动“路线图”来了_热点关注 - 北京市人民政府外事办公室-2025-08-31 17-13-51.md
2025-09-02 10:07:01,773 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 10:07:01,775 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:01,775 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 10:07:01,970 - modules.gemini_client - INFO - 成功处理 政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md，耗时: 2.86秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:01,973 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100701_3321.json
2025-09-02 10:07:01,973 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/政府报告中的一种新岗位形态-36氪-2025-08-31 18-15-36.md
2025-09-02 10:07:01,973 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 10:07:01,980 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:01,981 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 10:07:02,731 - modules.gemini_client - INFO - 成功处理 Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md，耗时: 2.06秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:02,731 - modules.gemini_client - INFO - 成功处理 发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md，耗时: 2.47秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:02,735 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100702_3904.json
2025-09-02 10:07:02,735 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100702_3279.json
2025-09-02 10:07:02,735 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/发电的又一“大招”，来了_腾讯新闻-2025-08-31 17-27-51.md
2025-09-02 10:07:02,735 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Scrapy 2.13 documentation — Scrapy 2.13.3 documentation-2025-08-31 09-30-55.md
2025-09-02 10:07:02,735 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 10:07:02,736 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 10:07:02,752 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:02,752 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:02,752 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: 川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 10:07:02,753 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 10:07:03,358 - modules.gemini_client - INFO - 成功处理 Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md，耗时: 2.88秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:03,359 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100703_0987.json
2025-09-02 10:07:03,359 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Anthropic 的企业妙招：捆绑 Claude Code，每位用户 60 美元-2025-08-31 06-43-00.md
2025-09-02 10:07:03,359 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 10:07:03,363 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash-lite (优先级: 3, 成功率: 100.00%)
2025-09-02 10:07:03,363 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash-lite 处理文件: Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 10:07:03,939 - modules.gemini_client - INFO - 成功处理 单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md，耗时: 2.16秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:03,940 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100703_6036.json
2025-09-02 10:07:03,940 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/单机年发电量1亿度！全球最大海上风电机组完成吊装-2025-08-31 06-57-08.md
2025-09-02 10:07:03,940 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 10:07:03,942 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:07:03,942 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 6.4 秒
2025-09-02 10:07:04,388 - modules.gemini_client - INFO - 成功处理 大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md，耗时: 2.41秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:04,390 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100704_2519.json
2025-09-02 10:07:04,390 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/大模型微调到底有没有技术含量，或者说技术含量到底有多大？-2025-08-31 19-02-09.md
2025-09-02 10:07:04,390 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 10:07:04,391 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:07:04,391 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 5.9 秒
2025-09-02 10:07:04,581 - modules.gemini_client - INFO - 成功处理 川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md，耗时: 1.83秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:04,586 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100704_4660.json
2025-09-02 10:07:04,586 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/川大团队研发高精度行人追踪技术，实现密集人群环境下行人移动及步态追踪-2025-08-31 00-19-09.md
2025-09-02 10:07:04,586 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 10:07:04,589 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:07:04,589 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 5.7 秒
2025-09-02 10:07:05,939 - modules.gemini_client - INFO - 成功处理 Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md，耗时: 2.58秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:05,941 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100705_1825.json
2025-09-02 10:07:05,941 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Meta王炸DINOv3：视觉自监督新巅峰！7B模型狂揽多任务SOTA-2025-09-01 09-17-35.md
2025-09-02 10:07:05,941 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 10:07:05,946 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:07:05,946 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 4.4 秒
2025-09-02 10:07:06,828 - modules.gemini_client - INFO - 成功处理 AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md，耗时: 4.07秒，模型: gemini-2.5-flash-lite
2025-09-02 10:07:06,830 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100706_0306.json
2025-09-02 10:07:06,830 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/AI写的“强化学习害了我女儿”刷屏，我们比了比哪个大模型最会发疯-品玩-2025-09-01 06-37-28.md
2025-09-02 10:07:06,830 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 10:07:06,832 - modules.gemini_client - WARNING - 所有模型都已达到速率限制
2025-09-02 10:07:06,832 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash 需要等待 3.5 秒
2025-09-02 10:07:10,324 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:07:10,324 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:07:10,324 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:07:10,324 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:07:10,324 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash: 已达到每分钟请求限制 (13/10)
2025-09-02 10:07:10,325 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 24.0 秒
2025-09-02 10:07:10,325 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 24.0 秒
2025-09-02 10:07:10,325 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 24.0 秒
2025-09-02 10:07:10,325 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 24.0 秒
2025-09-02 10:07:10,325 - modules.rate_limiter - INFO - 模型 gemini-2.5-pro 需要等待 24.0 秒
2025-09-02 10:07:34,281 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:07:34,281 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:07:34,281 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:07:34,281 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:07:34,281 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-pro: 已达到每分钟请求限制 (8/5)
2025-09-02 10:07:34,282 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 20.9 秒
2025-09-02 10:07:34,282 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 20.9 秒
2025-09-02 10:07:34,282 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 20.9 秒
2025-09-02 10:07:34,282 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 20.9 秒
2025-09-02 10:07:34,282 - modules.rate_limiter - INFO - 模型 gemini-2.5-flash-lite 需要等待 20.9 秒
2025-09-02 10:07:55,179 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (17/15)
2025-09-02 10:07:55,180 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (17/15)
2025-09-02 10:07:55,180 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (17/15)
2025-09-02 10:07:55,180 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (17/15)
2025-09-02 10:07:55,180 - modules.rate_limiter - WARNING - 等待后仍无法请求 gemini-2.5-flash-lite: 已达到每分钟请求限制 (17/15)
2025-09-02 10:07:55,193 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md, 所有模型都已达到速率限制
2025-09-02 10:07:55,193 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md, 所有模型都已达到速率限制
2025-09-02 10:07:55,193 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md, 所有模型都已达到速率限制
2025-09-02 10:07:55,193 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md, 所有模型都已达到速率限制
2025-09-02 10:07:55,193 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100755_7031.json
2025-09-02 10:07:55,193 - modules.storage - WARNING - 标记文件处理失败: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md, 所有模型都已达到速率限制
2025-09-02 10:07:55,194 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100755_7158.json
2025-09-02 10:07:55,194 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100755_7590.json
2025-09-02 10:07:55,194 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100755_1580.json
2025-09-02 10:07:55,194 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/Tier 1一哥博世端到端终于走到量产，还是一段式！-2025-08-31 00-12-15.md
2025-09-02 10:07:55,194 - modules.storage - INFO - 汇总结果已保存: data/summaries/summary_20250902_100755_5411.json
2025-09-02 10:07:55,194 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/黄仁勋：Blackwell GPU或将进入中国市场--快科技--科技改变未来-2025-08-30 23-59-47.md
2025-09-02 10:07:55,194 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/马斯克再度炮轰激光雷达：恶劣天气拉胯 摄像头更可靠！--快科技--科技改变未来-2025-08-30 07-38-55.md
2025-09-02 10:07:55,194 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/常生龙：在“人工智能+”行动中推行更富成效的学习方式_上观新闻-2025-09-01 06-36-50.md
2025-09-02 10:07:55,194 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 10:07:55,195 - modules.summarizer - INFO - 文件处理完成: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/经济日报：十年跨入智能社会，你准备好了吗_舆论场_澎湃新闻-The Paper-2025-08-31 17-13-24.md
2025-09-02 10:07:55,195 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 10:07:55,195 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 10:07:55,195 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 10:07:55,195 - modules.summarizer - INFO - 开始处理文件: /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox/为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 10:07:55,213 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:07:55,213 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:07:55,214 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:07:55,214 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:07:55,214 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
2025-09-02 10:07:55,214 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 小扎AI团队乱成一锅粥：ChatGPT功臣刚来就想跑路，28岁新领导「难堪大任」 - 爱范儿-2025-08-31 00-04-04.md
2025-09-02 10:07:55,214 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 具身智能高质量合成数据集开源发布-2025-08-31 22-49-36.md
2025-09-02 10:07:55,214 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 高考赢家，批量“放弃”清华北大？-虎嗅网-2025-08-31 06-54-02.md
2025-09-02 10:07:55,214 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 追觅官宣「造车」，我只看到一个「急」字 - 爱范儿-2025-08-30 07-49-20.md
2025-09-02 10:07:55,214 - modules.gemini_client - INFO - 使用模型 gemini-2.5-flash 处理文件: 为什么Claude Code 如此好？「以及如何在自己工作流中复刻Claude Code体验」_手机新浪网-2025-08-31 06-43-12.md
2025-09-02 10:08:12,257 - __main__ - INFO - 新闻摘要程序启动
2025-09-02 10:08:12,257 - __main__ - INFO - 正在初始化应用组件...
2025-09-02 10:08:12,258 - modules.error_handler - INFO - 重试处理器已启动
2025-09-02 10:08:12,258 - modules.rate_limiter - INFO - 已加载速率限制历史记录: 3 个模型
2025-09-02 10:08:12,258 - modules.gemini_client - INFO - GeminiClient已初始化，支持 3 个模型
2025-09-02 10:08:12,258 - modules.storage - INFO - 加载了 47 个已处理文件记录
2025-09-02 10:08:12,258 - __main__ - INFO - 应用组件初始化完成
2025-09-02 10:08:12,258 - modules.rate_limiter - INFO - 选择模型: gemini-2.5-flash (优先级: 1, 成功率: 100.00%)
