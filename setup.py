#!/usr/bin/env python3
"""
新闻摘要程序安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件作为长描述
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# 读取requirements文件
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="news-summarizer",
    version="1.0.0",
    author="<PERSON>",
    author_email="<EMAIL>",
    description="一个基于Python的新闻摘要程序，使用Google Gemini API进行智能汇总",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/jeff/news-summarizer",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Text Processing :: Linguistic",
        "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: News/Diary",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "news-summarizer=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.ini"],
    },
    zip_safe=False,
)
