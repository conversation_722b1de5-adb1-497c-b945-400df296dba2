#!/usr/bin/env python3
"""
测试新的增强prompt系统
"""

import asyncio
import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config import Config
from modules.gemini_client import GeminiClient

async def test_enhanced_prompts():
    """测试增强的提示词系统"""
    print("=== 测试增强Prompt系统 ===\n")
    
    # 加载配置
    config = Config.load()
    
    # 初始化客户端
    client = GeminiClient(
        api_key=config.gemini_api_key,
        config=config
    )
    
    # 测试用例：不同类型的内容
    test_cases = [
        {
            "content": "NVIDIA发布了全新的Jetson Thor机器人计算平台，专为通用机器人和物理AI应用设计。该平台集成了强大的AI处理能力，支持实时推理和多传感器数据融合。",
            "filename": "NVIDIA Jetson Thor技术发布.md",
            "expected_type": "tech"
        },
        {
            "content": "据报道，小米公司今日宣布完成对纬钛机器人的新一轮融资，投资金额达数亿元。此次投资将用于加速视触觉传感器技术的研发和商业化进程。",
            "filename": "小米投资纬钛机器人融资新闻.md", 
            "expected_type": "business"
        },
        {
            "content": "国务院印发《关于促进'人工智能+'行动的指导意见》，明确提出要在制造业、医疗、教育等重点领域深化人工智能应用，到2027年实现人工智能技术在各行业的广泛应用。",
            "filename": "国务院人工智能+政策文件.md",
            "expected_type": "policy"
        }
    ]
    
    # 逐个测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- 测试用例 {i}: {test_case['expected_type']} ---")
        print(f"文件名: {test_case['filename']}")
        print(f"内容: {test_case['content'][:100]}...")
        
        try:
            # 调用API
            result = await client.generate_summary_async(
                content=test_case['content'],
                filename=test_case['filename']
            )
            
            print(f"\n✅ 处理成功:")
            print(f"  检测类型: {result.content_type}")
            print(f"  标题: {result.title}")
            print(f"  核心事实: {result.core_facts}")
            print(f"  关键点: {result.key_points}")
            print(f"  重要性: {result.importance_level} ({result.importance_reason})")
            print(f"  关键词: {result.keywords}")
            print(f"  处理状态: {result.processing_status}")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
        
        print("\n" + "="*60 + "\n")
        
        # 避免触发速率限制
        await asyncio.sleep(2)
    
    print("🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(test_enhanced_prompts())