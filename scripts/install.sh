#!/bin/bash
# 新闻摘要程序安装脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python版本
check_python() {
    log_info "检查Python版本..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 9 ]; then
            log_success "Python版本: $PYTHON_VERSION (符合要求)"
            PYTHON_CMD="python3"
        else
            log_error "Python版本过低: $PYTHON_VERSION (需要3.9+)"
            exit 1
        fi
    else
        log_error "未找到Python3，请先安装Python 3.9+"
        exit 1
    fi
}

# 检查pip
check_pip() {
    log_info "检查pip..."
    
    if command -v pip3 &> /dev/null; then
        log_success "pip3已安装"
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        log_success "pip已安装"
        PIP_CMD="pip"
    else
        log_error "未找到pip，请先安装pip"
        exit 1
    fi
}

# 创建虚拟环境
create_venv() {
    log_info "创建虚拟环境..."
    
    if [ -d "venv" ]; then
        log_warning "虚拟环境已存在，跳过创建"
    else
        $PYTHON_CMD -m venv venv
        log_success "虚拟环境创建完成"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    log_success "虚拟环境已激活"
}

# 升级pip
upgrade_pip() {
    log_info "升级pip..."
    pip install --upgrade pip
    log_success "pip升级完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖包..."
    
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        log_success "依赖包安装完成"
    else
        log_error "未找到requirements.txt文件"
        exit 1
    fi
}

# 创建配置文件
setup_config() {
    log_info "设置配置文件..."
    
    # 复制环境变量模板
    if [ -f ".env.example" ] && [ ! -f ".env" ]; then
        cp .env.example .env
        log_success "环境变量文件已创建: .env"
        log_warning "请编辑.env文件，设置您的Gemini API密钥"
    fi
    
    # 创建必要的目录
    mkdir -p data/summaries
    mkdir -p logs
    log_success "目录结构创建完成"
}

# 检查API密钥
check_api_key() {
    log_info "检查API密钥配置..."
    
    if [ -f ".env" ]; then
        if grep -q "GEMINI_API_KEY=your_gemini_api_key_here" .env; then
            log_warning "请在.env文件中设置您的Gemini API密钥"
            log_warning "编辑.env文件，将GEMINI_API_KEY设置为您的实际API密钥"
        else
            log_success "API密钥配置检查完成"
        fi
    else
        log_warning "未找到.env文件，请手动创建并设置API密钥"
    fi
}

# 运行测试
run_tests() {
    log_info "运行基本测试..."
    
    # 测试配置加载
    if $PYTHON_CMD -c "from config import Config; Config.load()" 2>/dev/null; then
        log_success "配置模块测试通过"
    else
        log_warning "配置模块测试失败，请检查配置"
    fi
    
    # 测试模块导入
    if $PYTHON_CMD -c "from modules import *" 2>/dev/null; then
        log_success "模块导入测试通过"
    else
        log_warning "模块导入测试失败，请检查依赖"
    fi
}

# 创建启动脚本
create_launcher() {
    log_info "创建启动脚本..."
    
    cat > run.sh << 'EOF'
#!/bin/bash
# 新闻摘要程序启动脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 激活虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
    echo "虚拟环境已激活"
else
    echo "警告: 未找到虚拟环境"
fi

# 检查API密钥
if [ ! -f ".env" ]; then
    echo "错误: 未找到.env文件，请先运行安装脚本"
    exit 1
fi

# 运行程序
python main.py "$@"
EOF
    
    chmod +x run.sh
    log_success "启动脚本创建完成: run.sh"
}

# 显示使用说明
show_usage() {
    echo ""
    echo "=========================================="
    echo "新闻摘要程序安装完成！"
    echo "=========================================="
    echo ""
    echo "下一步操作："
    echo "1. 编辑.env文件，设置您的Gemini API密钥"
    echo "   GEMINI_API_KEY=your_actual_api_key"
    echo ""
    echo "2. 根据需要修改data/config.ini配置文件"
    echo ""
    echo "3. 运行程序："
    echo "   ./run.sh                    # 启动监控模式"
    echo "   ./run.sh --scan             # 手动扫描处理"
    echo "   ./run.sh --status           # 查看状态"
    echo "   ./run.sh --help             # 查看帮助"
    echo ""
    echo "4. 或者直接使用Python："
    echo "   source venv/bin/activate"
    echo "   python main.py"
    echo ""
    echo "=========================================="
}

# 主安装流程
main() {
    echo "=========================================="
    echo "新闻摘要程序安装脚本"
    echo "=========================================="
    echo ""
    
    # 检查系统要求
    check_python
    check_pip
    
    # 安装程序
    create_venv
    upgrade_pip
    install_dependencies
    
    # 配置程序
    setup_config
    check_api_key
    
    # 测试和完成
    run_tests
    create_launcher
    
    # 显示使用说明
    show_usage
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查上面的错误信息"; exit 1' ERR

# 运行主函数
main "$@"
