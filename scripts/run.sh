#!/bin/bash
# 新闻摘要程序运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_DIR"

# 检查虚拟环境
check_venv() {
    if [ -d "venv" ]; then
        log_info "激活虚拟环境..."
        source venv/bin/activate
        log_success "虚拟环境已激活"
    else
        log_warning "未找到虚拟环境，使用系统Python"
    fi
}

# 检查配置文件
check_config() {
    if [ ! -f ".env" ]; then
        log_error "未找到.env文件，请先运行安装脚本"
        echo "运行: bash scripts/install.sh"
        exit 1
    fi
    
    # 检查API密钥
    if grep -q "your_gemini_api_key_here" .env 2>/dev/null; then
        log_error "请在.env文件中设置您的Gemini API密钥"
        exit 1
    fi
    
    log_success "配置文件检查通过"
}

# 检查依赖
check_dependencies() {
    log_info "检查Python依赖..."
    
    if ! python -c "import google.generativeai, watchdog, schedule, markdown" 2>/dev/null; then
        log_error "缺少必要的Python依赖包"
        log_info "请运行: pip install -r requirements.txt"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建必要目录
ensure_directories() {
    mkdir -p data/summaries
    mkdir -p logs
    log_info "目录结构检查完成"
}

# 显示系统信息
show_system_info() {
    log_info "系统信息:"
    echo "  Python版本: $(python --version)"
    echo "  工作目录: $(pwd)"
    echo "  时间: $(date)"
    echo ""
}

# 运行程序
run_program() {
    log_info "启动新闻摘要程序..."
    echo ""
    
    # 传递所有参数给主程序
    python main.py "$@"
}

# 显示帮助信息
show_help() {
    echo "新闻摘要程序运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  无参数              启动监控模式"
    echo "  --scan, -s          执行手动扫描"
    echo "  --status            显示状态信息"
    echo "  --report [格式]     生成报告 (markdown/json)"
    echo "  --init              初始化配置"
    echo "  --help, -h          显示此帮助信息"
    echo "  --log-level LEVEL   设置日志级别"
    echo ""
    echo "示例:"
    echo "  $0                  # 启动监控模式"
    echo "  $0 --scan           # 手动扫描处理"
    echo "  $0 --status         # 查看状态"
    echo "  $0 --report         # 生成Markdown报告"
    echo "  $0 --report json    # 生成JSON报告"
    echo ""
}

# 主函数
main() {
    # 检查是否请求帮助
    if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
        show_help
        exit 0
    fi
    
    # 系统检查
    check_venv
    check_config
    check_dependencies
    ensure_directories
    
    # 显示系统信息
    show_system_info
    
    # 运行程序
    run_program "$@"
}

# 错误处理
trap 'log_error "程序运行过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
