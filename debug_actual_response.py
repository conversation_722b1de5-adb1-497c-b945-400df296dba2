#!/usr/bin/env python3
"""
调试实际的API响应
"""

import os
import re
from dotenv import load_dotenv
import google.generativeai as genai

def test_actual_api_response():
    """测试实际的API响应"""
    load_dotenv()
    
    # 配置API
    genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
    model = genai.GenerativeModel("gemini-2.0-flash-exp")
    
    # 使用和程序中相同的提示词
    prompt = """
作为专业新闻汇总助手，请对以下新闻内容进行结构化汇总。

请按照以下JSON格式返回结果：
{
    "title": "新闻标题",
    "core_facts": "核心事实（包含5W1H：时间、地点、人物、事件、原因、影响）",
    "key_points": ["要点1", "要点2", "要点3"],
    "importance_level": "高/中/低",
    "importance_reason": "重要性评估理由",
    "keywords": ["关键词1", "关键词2", "关键词3"]
}

汇总要求：
1. 提取核心事实，包含时间、地点、人物、事件、原因、影响等要素
2. 突出3-5个关键信息点
3. 评估新闻重要性等级（高/中/低）并说明理由
4. 生成3-5个相关关键词标签
5. 保持客观中立，避免主观判断

新闻内容：

    # 测试新闻
    
    今日，中国国产x86 CPU首次成功应用于桌面AI PC，标志着在与Intel、AMD等国际巨头的竞争中迈出了重要一步。
    
    据悉，这款国产CPU在日常办公应用场景下表现稳定。
    
"""
    
    print("🔍 测试实际API响应")
    print("=" * 60)
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text
        
        print(f"📄 原始响应长度: {len(response_text)}")
        print(f"📄 原始响应（repr格式）:")
        print(repr(response_text))
        print("\n📄 原始响应（正常格式）:")
        print(response_text)
        print("\n" + "="*60)
        
        # 测试解析过程
        print("🧪 测试解析过程:")
        
        # 第一步：清理
        cleaned_text = response_text.strip()
        print(f"步骤1 - 去空格后: {repr(cleaned_text[:50])}...")
        
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]
            print("步骤2 - 已去除```json")
            
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]
            print("步骤3 - 已去除```")
            
        cleaned_text = cleaned_text.strip()
        print(f"步骤4 - 最终清理: {repr(cleaned_text[:50])}...")
        
        # 第二步：JSON解析
        import json
        try:
            data = json.loads(cleaned_text)
            print("✅ 直接JSON解析成功!")
            print(f"解析结果: {data}")
        except Exception as e:
            print(f"❌ 直接JSON解析失败: {e}")
            print(f"❌ 错误类型: {type(e).__name__}")
            print(f"❌ 错误消息: {repr(str(e))}")
            
            # 尝试正则提取
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print(f"🔍 正则提取JSON: {repr(json_str[:100])}...")
                try:
                    data = json.loads(json_str)
                    print("✅ 正则JSON解析成功!")
                    print(f"解析结果: {data}")
                except Exception as e2:
                    print(f"❌ 正则JSON解析失败: {e2}")
                    print(f"❌ 错误类型: {type(e2).__name__}")
                    print(f"❌ 错误消息: {repr(str(e2))}")
            else:
                print("❌ 正则无法找到JSON")
                
    except Exception as e:
        print(f"❌ API调用失败: {e}")

if __name__ == "__main__":
    test_actual_api_response()