#!/usr/bin/env python3
"""
详细调试Gemini客户端问题
"""

import os
import traceback
from dotenv import load_dotenv
from modules.gemini_client import GeminiClient

# 重写调试版本的_parse_response方法
def debug_parse_response(self, response_text, content_type):
    """调试版本的解析响应方法"""
    print(f"🔍 开始解析响应，内容类型: {content_type}")
    print(f"📄 响应文本: {repr(response_text[:100])}...")
    
    try:
        import json
        import re
        
        # 首先尝试去除markdown代码块标记
        cleaned_text = response_text.strip()
        print(f"📝 原始文本长度: {len(response_text)}")
        
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]  # 去除```json
            print("✅ 去除了```json开头")
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]  # 去除```
            print("✅ 去除了```结尾")
        cleaned_text = cleaned_text.strip()
        
        print(f"📝 清理后文本长度: {len(cleaned_text)}")
        print(f"📝 清理后文本前100字符: {repr(cleaned_text[:100])}")
        
        # 尝试直接解析清理后的文本
        try:
            data = json.loads(cleaned_text)
            print("✅ 直接JSON解析成功")
        except Exception as e:
            print(f"❌ 直接JSON解析失败: {e}")
            # 如果直接解析失败，尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print(f"📝 正则提取的JSON前100字符: {repr(json_str[:100])}")
                data = json.loads(json_str)
                print("✅ 正则JSON解析成功")
            else:
                raise ValueError("无法找到有效的JSON格式")
        
        from modules.gemini_client import SummaryResponse
        result = SummaryResponse(
            title=data.get("title", "未知标题"),
            core_facts=data.get("core_facts", ""),
            key_points=data.get("key_points", []),
            importance_level=data.get("importance_level", "中"),
            importance_reason=data.get("importance_reason", ""),
            keywords=data.get("keywords", []),
            content_type=content_type.value,
            processing_status="success"
        )
        print(f"✅ 解析完成，标题: {result.title}")
        return result
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        traceback.print_exc()
        raise e

# 猴子补丁，临时替换方法
GeminiClient._parse_response = debug_parse_response

def main():
    load_dotenv()
    
    client = GeminiClient(api_key=os.getenv("GEMINI_API_KEY"))
    
    test_content = """
    # 测试新闻
    今日，中国国产x86 CPU首次应用于桌面AI PC。
    """
    
    print("🚀 开始详细调试测试...")
    print("=" * 60)
    
    try:
        result = client.generate_summary(test_content, "测试.md")
        print(f"🎉 最终结果:")
        print(f"  处理状态: {result.processing_status}")
        print(f"  标题: {result.title}")
        print(f"  核心事实: {result.core_facts[:100]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()