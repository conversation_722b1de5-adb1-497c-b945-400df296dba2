#!/usr/bin/env python3
"""
诊断JSON解析错误
"""

import json

def test_json_errors():
    """测试各种JSON解析错误"""
    
    test_cases = [
        # 情况1：以换行符和引号开头的无效JSON
        '\n    "title"',
        
        # 情况2：不完整的JSON
        '{\n    "title"',
        
        # 情况3：有效的JSON
        '{\n    "title": "测试"\n}',
        
        # 情况4：Markdown包装的JSON
        '```json\n{\n    "title": "测试"\n}\n```'
    ]
    
    for i, test_str in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {repr(test_str)}")
        try:
            result = json.loads(test_str)
            print(f"✅ 解析成功: {result}")
        except Exception as e:
            print(f"❌ 解析失败: {type(e).__name__}: {repr(str(e))}")

if __name__ == "__main__":
    test_json_errors()