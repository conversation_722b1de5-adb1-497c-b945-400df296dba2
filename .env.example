# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyCc_lcMhDP4F9Vmj9quWD0PZ8KdLBOQMGQ

# File Monitoring Configuration
WATCH_DIRECTORY=/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox
SCAN_INTERVAL_MINUTES=1
FILE_EXTENSIONS=.md

# Processing Configuration
MAX_CONCURRENT_FILES=5
CHUNK_SIZE_CHARS=10000
RETRY_ATTEMPTS=3
RETRY_DELAY_SECONDS=5

# Output Configuration
OUTPUT_DIRECTORY=data/summaries
LOG_DIRECTORY=logs
LOG_LEVEL=INFO

# Advanced Configuration (optional)
ENABLE_REAL_TIME_MONITORING=true
ENABLE_BATCH_PROCESSING=true
PROGRESS_UPDATE_INTERVAL=10
