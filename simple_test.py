#!/usr/bin/env python3
"""
简化测试脚本
"""

import os
import sys
from dotenv import load_dotenv

# 添加到路径
sys.path.append('.')

# 重新导入模块
if 'modules.gemini_client' in sys.modules:
    del sys.modules['modules.gemini_client']

from modules.gemini_client import GeminiClient

def main():
    load_dotenv()
    
    # 创建客户端
    client = GeminiClient(
        api_key=os.getenv("GEMINI_API_KEY"),
        model_name="gemini-2.0-flash-exp"
    )
    
    # 测试内容
    content = "今天是个好天气。"
    
    print("🧪 简化测试...")
    
    try:
        result = client.generate_summary(content, "test.md")
        
        print(f"状态: {result.processing_status}")
        if result.processing_status == "error":
            print(f"错误: {result.error_message}")
        else:
            print(f"标题: {result.title}")
            
    except Exception as e:
        print(f"异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()