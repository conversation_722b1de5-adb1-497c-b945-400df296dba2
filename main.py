#!/usr/bin/env python3
"""
新闻摘要程序主入口

提供命令行界面和应用控制功能，包括：
- 命令行参数解析
- 应用初始化和配置
- 监控模式和手动处理
- 进度显示和状态查询
- 优雅退出和错误处理
"""

import argparse
import signal
import sys
import time
import logging
from pathlib import Path
from typing import Optional
import threading
from datetime import datetime

# 导入自定义模块
from config import Config, setup_logging
from modules.gemini_client import GeminiClient
from modules.file_monitor import FileMonitor
from modules.storage import Storage
from modules.summarizer import ContentSummarizer
from modules.error_handler import ErrorHandler


class NewsForgeApp:
    """
    新闻摘要应用主类
    
    这是整个新闻摘要系统的核心控制器，负责协调所有组件的工作。
    主要功能包括：
    - 应用生命周期管理（初始化、启动、关闭）
    - 文件监控模式和手动扫描模式
    - 进度跟踪和状态显示
    - 错误处理和统计信息管理
    - 报告生成和导出
    
    Attributes:
        config (Config): 应用配置实例
        logger (logging.Logger): 日志记录器
        is_running (bool): 运行状态标志
        shutdown_event (threading.Event): 关闭事件
        gemini_client (GeminiClient): Gemini API客户端
        storage (Storage): 存储管理器
        summarizer (ContentSummarizer): 内容汇总处理器
        file_monitor (FileMonitor): 文件监控器
        error_handler (ErrorHandler): 错误处理器
        stats (Dict[str, Any]): 运行统计信息
    """
    
    def __init__(self, config: Config):
        """
        初始化应用
        
        Args:
            config: 应用配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 应用状态
        self.is_running = False
        self.shutdown_event = threading.Event()
        
        # 核心组件
        self.gemini_client = None
        self.storage = None
        self.summarizer = None
        self.file_monitor = None
        self.error_handler = None
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'files_processed': 0,
            'files_failed': 0,
            'api_calls': 0
        }
        
        # 初始化组件
        self._initialize_components()
        
        # 设置信号处理
        self._setup_signal_handlers()
    
    def _initialize_components(self) -> None:
        """
        初始化应用的所有核心组件
        
        这个方法按照依赖关系的顺序初始化各个组件：
        1. 创建必需的目录结构
        2. 初始化错误处理器（用于全局错误管理）
        3. 初始化Gemini API客户端（用于AI汇总）
        4. 初始化存储管理器（用于数据持久化）
        5. 初始化内容汇总处理器（核心处理逻辑）
        6. 初始化文件监控器（可选，用于实时监控）
        
        Raises:
            Exception: 如果任何组件初始化失败，将抛出异常并记录错误
        """
        try:
            self.logger.info("正在初始化应用组件...")
            
            # 创建必需的目录结构（输出目录、日志目录等）
            self.config.create_directories()
            
            # 初始化错误处理器 - 负责全局错误管理、重试机制和错误日志记录
            error_log_file = Path(self.config.log_directory) / "errors.log"
            self.error_handler = ErrorHandler(
                max_retries=self.config.retry_attempts,        # 最大重试次数
                retry_delay=self.config.retry_delay_seconds,   # 重试延迟时间
                error_log_file=str(error_log_file)             # 错误日志文件路径
            )
            
            # 初始化智能Gemini API客户端 - 负责与Google Gemini AI服务通信，支持速率限制
            self.gemini_client = GeminiClient(
                api_key=self.config.gemini_api_key,            # API密钥
                config=self.config,                            # 配置对象（包含模型限制信息）
                retry_attempts=self.config.retry_attempts,     # API调用重试次数
                retry_delay=self.config.retry_delay_seconds    # API调用重试延迟
            )
            
            # 初始化存储管理器 - 负责汇总结果的存储和已处理文件的跟踪
            self.storage = Storage(
                output_directory=self.config.output_directory,           # 输出目录路径
                processed_file_tracker=self.config.processed_file_tracker  # 已处理文件跟踪文件路径
            )
            
            # 初始化内容汇总处理器 - 核心业务逻辑，协调AI客户端和存储管理器
            self.summarizer = ContentSummarizer(
                gemini_client=self.gemini_client,              # 注入AI客户端依赖
                storage=self.storage,                          # 注入存储管理器依赖
                max_concurrent_files=self.config.max_concurrent_files  # 最大并发处理文件数
            )
            
            # 设置进度回调函数 - 用于在处理过程中显示进度信息
            self.summarizer.set_progress_callback(self._progress_callback)
            
            # 初始化文件监控器
            if self.config.enable_real_time_monitoring:
                self.file_monitor = FileMonitor(
                    watch_directory=self.config.watch_directory,
                    file_extensions=self.config.file_extensions,
                    scan_interval_minutes=self.config.scan_interval_minutes,
                    ignore_hidden_files=self.config.ignore_hidden_files,
                    ignore_temp_files=self.config.ignore_temp_files
                )
                
                # 设置文件变化回调
                self.file_monitor.set_callback(self._file_change_callback)
            
            self.logger.info("应用组件初始化完成")
            
        except Exception as e:
            self.logger.critical(f"初始化应用组件失败: {e}")
            if self.error_handler:
                self.error_handler.handle_error(e, {'component': 'initialization'})
            raise
    
    def _setup_signal_handlers(self) -> None:
        """
        设置系统信号处理器以实现优雅退出
        
        注册SIGINT（Ctrl+C）和SIGTERM（终止信号）的处理函数，
        确保程序在接收到中断信号时能够优雅地关闭所有资源。
        """
        def signal_handler(signum, _frame):
            """
            信号处理函数
            
            Args:
                signum (int): 接收到的信号编号
                _frame: 当前执行框架（未使用）
            """
            self.logger.info("收到信号 %d，正在优雅退出...", signum)
            self.shutdown()
        
        # 注册中断信号处理器（Ctrl+C）
        signal.signal(signal.SIGINT, signal_handler)
        # 注册终止信号处理器
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _progress_callback(self, current: int, total: int, status: str) -> None:
        """
        进度显示回调函数
        
        用于在文件处理过程中显示实时进度信息，包括当前进度数、
        总数、百分比和当前状态。使用回车符实现同行更新显示。
        
        Args:
            current (int): 当前已完成的文件数量
            total (int): 总文件数量
            status (str): 当前处理状态描述
        """
        if total > 0:
            # 计算完成百分比
            percentage = (current / total) * 100
            # 使用回车符实现同行更新，显示进度条效果
            print(f"\r进度: {current}/{total} ({percentage:.1f}%) - {status}", end="", flush=True)
            
            # 处理完成时换行
            if current == total:
                print()  # 换行以便后续输出
    
    def _file_change_callback(self, file_path: str, event_type: str) -> None:
        """
        文件变化事件回调函数
        
        当文件监控器检测到文件系统事件时调用此函数，
        负责处理新创建、修改或移动的文件。
        
        处理流程：
        1. 记录文件变化事件
        2. 调用汇总处理器处理文件
        3. 更新处理统计信息
        4. 记录处理结果
        
        Args:
            file_path (str): 发生变化的文件路径
            event_type (str): 事件类型（created/modified/moved）
        """
        try:
            self.logger.info("检测到文件%s: %s", event_type, file_path)
            
            # 调用内容汇总处理器处理文件
            result = self.summarizer.process_file(Path(file_path))
            
            # 根据处理结果更新统计信息
            if result.processing_status.startswith('success'):
                self.stats['files_processed'] += 1  # 成功处理文件数
                self.stats['api_calls'] += 1        # API调用次数
            elif result.processing_status == 'error':
                self.stats['files_failed'] += 1     # 失败文件数
            
            self.logger.info("文件处理完成: %s - %s", file_path, result.processing_status)
            
        except Exception as e:
            # 记录错误并通过错误处理器处理
            self.logger.error("处理文件变化失败: %s", e)
            if self.error_handler:
                self.error_handler.handle_error(e, {
                    'file_path': file_path,
                    'event_type': event_type,
                    'task_type': 'file_change'
                })
    
    def start_monitoring(self) -> None:
        """启动监控模式"""
        if self.is_running:
            self.logger.warning("应用已在运行中")
            return
        
        try:
            self.logger.info("启动新闻摘要应用...")
            self.is_running = True
            self.stats['start_time'] = datetime.now()
            
            # 启动文件监控
            if self.file_monitor:
                self.file_monitor.start_monitoring()
                self.logger.info("文件监控已启动")
            
            # 显示应用状态
            self._display_status()
            
            # 主循环
            while self.is_running and not self.shutdown_event.is_set():
                try:
                    # 每分钟显示一次状态
                    if self.shutdown_event.wait(60):
                        break
                    
                    # 显示简要状态
                    self._display_brief_status()
                    
                except KeyboardInterrupt:
                    break
            
        except Exception as e:
            self.logger.error(f"监控模式运行失败: {e}")
            if self.error_handler:
                self.error_handler.handle_error(e, {'task_type': 'monitoring'})
        finally:
            self.shutdown()
    
    def manual_scan(self) -> None:
        """手动扫描和处理"""
        try:
            self.logger.info("开始手动扫描...")
            
            # 扫描目录
            if self.file_monitor:
                files = self.file_monitor.scan_directory()
            else:
                # 如果没有文件监控器，直接扫描目录
                watch_path = Path(self.config.watch_directory)
                files = []
                for ext in self.config.file_extensions:
                    files.extend(watch_path.rglob(f"*{ext}"))
            
            if not files:
                print("未发现需要处理的文件")
                return
            
            # 过滤已处理的文件
            unprocessed_files = [
                f for f in files 
                if not self.storage.processed_tracker.is_processed(f)
            ]
            
            if not unprocessed_files:
                print("所有文件都已处理")
                return
            
            print(f"发现 {len(unprocessed_files)} 个未处理文件")
            
            # 批量处理
            results = self.summarizer.process_batch(unprocessed_files, self._progress_callback)
            
            # 统计结果
            successful = sum(1 for r in results if r.processing_status.startswith('success'))
            failed = sum(1 for r in results if r.processing_status == 'error')
            skipped = sum(1 for r in results if r.processing_status == 'skipped')
            
            print(f"\n处理完成:")
            print(f"  成功: {successful}")
            print(f"  失败: {failed}")
            print(f"  跳过: {skipped}")
            
            # 更新统计
            self.stats['files_processed'] += successful
            self.stats['files_failed'] += failed
            self.stats['api_calls'] += successful
            
        except Exception as e:
            self.logger.error(f"手动扫描失败: {e}")
            if self.error_handler:
                self.error_handler.handle_error(e, {'task_type': 'manual_scan'})
    
    def display_status(self) -> None:
        """显示详细状态"""
        self._display_status()
    
    def _display_status(self) -> None:
        """显示应用状态"""
        print("\n" + "="*60)
        print("新闻摘要应用状态")
        print("="*60)
        
        # 基本信息
        print(f"监控目录: {self.config.watch_directory}")
        print(f"输出目录: {self.config.output_directory}")
        print(f"文件类型: {', '.join(self.config.file_extensions)}")
        print(f"运行状态: {'运行中' if self.is_running else '已停止'}")
        
        if self.stats['start_time']:
            runtime = datetime.now() - self.stats['start_time']
            print(f"运行时间: {runtime}")
        
        # 处理统计
        print(f"\n处理统计:")
        print(f"  已处理文件: {self.stats['files_processed']}")
        print(f"  处理失败: {self.stats['files_failed']}")
        print(f"  API调用: {self.stats['api_calls']}")
        
        # 存储统计
        if self.storage:
            storage_stats = self.storage.get_statistics()
            print(f"  总处理记录: {storage_stats['total_processed']}")
            print(f"  成功率: {storage_stats['success_rate']:.1%}")
        
        # 文件监控状态
        if self.file_monitor:
            monitor_status = self.file_monitor.get_status()
            print(f"\n文件监控:")
            print(f"  监控状态: {'活跃' if monitor_status['is_monitoring'] else '停止'}")
            print(f"  已知文件: {monitor_status['known_files_count']}")
            if monitor_status['last_scan_time']:
                print(f"  最后扫描: {monitor_status['last_scan_time']}")
        
        # 错误统计
        if self.error_handler:
            error_stats = self.error_handler.get_error_statistics()
            health = self.error_handler.get_health_status()
            print(f"\n系统健康:")
            print(f"  健康状态: {health['status']} ({health['health_score']}/100)")
            print(f"  总错误数: {error_stats['total_errors']}")
            print(f"  重试队列: {error_stats['retry_queue_size']}")
        
        print("="*60)
    
    def _display_brief_status(self) -> None:
        """显示简要状态"""
        if self.stats['start_time']:
            runtime = datetime.now() - self.stats['start_time']
            hours = int(runtime.total_seconds() // 3600)
            minutes = int((runtime.total_seconds() % 3600) // 60)
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                  f"运行 {hours:02d}:{minutes:02d} | "
                  f"已处理 {self.stats['files_processed']} | "
                  f"失败 {self.stats['files_failed']} | "
                  f"API调用 {self.stats['api_calls']}")
    
    def show_rate_limit_status(self) -> None:
        """显示API速率限制状态"""
        if not self.gemini_client:
            print("Gemini客户端未初始化")
            return
        
        print("\n=== API速率限制状态 ===")
        try:
            status = self.gemini_client.get_rate_limit_status()
            
            for model_name, model_status in status.items():
                print(f"\n模型: {model_name}")
                print(f"  每分钟限制: {model_status['rpm_used']}/{model_status['rpm_limit']} "
                      f"(剩余: {model_status['rpm_available']})")
                print(f"  每天限制:   {model_status['rpd_used']}/{model_status['rpd_limit']} "
                      f"(剩余: {model_status['rpd_available']})")
                print(f"  总请求数:   {model_status['total_requests']}")
                print(f"  失败数:     {model_status['failed_requests']}")
                print(f"  成功率:     {model_status['success_rate']:.1%}")
                
                if model_status['last_request'] > 0:
                    import time
                    last_request_time = datetime.fromtimestamp(model_status['last_request'])
                    print(f"  最后请求:   {last_request_time.strftime('%H:%M:%S')}")
            
            # 显示当前最佳可用模型
            best_model = self.gemini_client.rate_limiter.get_best_available_model()
            if best_model:
                print(f"\n当前推荐模型: {best_model}")
            else:
                print(f"\n⚠️  所有模型都已达到速率限制")
                
        except Exception as e:
            self.logger.error(f"获取速率限制状态失败: {e}")
            print(f"获取速率限制状态失败: {e}")
        
        print("=" * 25)
    
    def generate_report(self, format_type: str = "markdown") -> None:
        """
        生成报告
        
        Args:
            format_type: 报告格式
        """
        try:
            if not self.storage:
                print("存储组件未初始化")
                return
            
            report = self.storage.generate_report(format_type)
            
            # 保存报告到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = Path(self.config.output_directory) / f"report_{timestamp}.{format_type}"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"报告已生成: {report_file}")
            
            # 如果是markdown格式，也显示到控制台
            if format_type == "markdown":
                print("\n" + "="*60)
                print(report[:1000] + "..." if len(report) > 1000 else report)
                print("="*60)
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            if self.error_handler:
                self.error_handler.handle_error(e, {'task_type': 'generate_report'})
    
    def shutdown(self) -> None:
        """优雅关闭应用"""
        if not self.is_running:
            return
        
        self.logger.info("正在关闭应用...")
        self.is_running = False
        self.shutdown_event.set()
        
        try:
            # 停止文件监控
            if self.file_monitor:
                self.file_monitor.stop_monitoring()
            
            # 停止错误处理器
            if self.error_handler:
                self.error_handler.stop_retry_processor()
            
            # 显示最终统计
            print("\n应用已停止")
            if self.stats['start_time']:
                runtime = datetime.now() - self.stats['start_time']
                print(f"总运行时间: {runtime}")
                print(f"总处理文件: {self.stats['files_processed']}")
                print(f"总API调用: {self.stats['api_calls']}")
            
            self.logger.info("应用已优雅关闭")
            
        except Exception as e:
            self.logger.error(f"关闭应用时发生错误: {e}")


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="新闻摘要程序 - 自动监控和汇总Markdown新闻文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 启动监控模式
  python main.py --scan             # 手动扫描处理
  python main.py --status           # 显示状态信息
  python main.py --report           # 生成汇总报告
  python main.py --init             # 初始化配置
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径 (默认: data/config.ini)'
    )
    
    parser.add_argument(
        '--scan', '-s',
        action='store_true',
        help='执行手动扫描和处理'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示应用状态信息'
    )
    
    parser.add_argument(
        '--report', '-r',
        choices=['markdown', 'json'],
        default='markdown',
        help='生成汇总报告 (默认: markdown)'
    )
    
    parser.add_argument(
        '--init',
        action='store_true',
        help='初始化配置和目录结构'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='设置日志级别'
    )
    
    parser.add_argument(
        '--rate-limits',
        action='store_true',
        help='显示API速率限制状态'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = Config.load(args.config)
        
        # 覆盖日志级别
        if args.log_level:
            config.log_level = args.log_level
        
        # 设置日志
        setup_logging(config)
        logger = logging.getLogger(__name__)
        
        logger.info("新闻摘要程序启动")
        
        # 初始化模式
        if args.init:
            print("正在初始化配置和目录...")
            config.create_directories()
            print("初始化完成")
            return
        
        # 创建应用实例
        app = NewsForgeApp(config)
        
        # 根据参数执行不同操作
        if args.scan:
            app.manual_scan()
        elif args.status:
            app.display_status()
        elif args.rate_limits:
            app.show_rate_limit_status()
        elif args.report:
            app.generate_report(args.report)
        else:
            # 默认启动监控模式
            app.start_monitoring()
    
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
    except Exception as e:
        print(f"程序运行失败: {e}")
        logging.error(f"程序运行失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
