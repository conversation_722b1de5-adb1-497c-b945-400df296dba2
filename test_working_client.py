#!/usr/bin/env python3
"""
测试工作版本的客户端
"""

import os
from dotenv import load_dotenv
from modules.gemini_client_working import GeminiClient

def main():
    """测试工作版本"""
    load_dotenv()
    
    client = GeminiClient(api_key=os.getenv("GEMINI_API_KEY"))
    
    test_content = """
    # 测试新闻
    今日，中国国产x86 CPU首次成功应用于桌面AI PC，标志着在与Intel、AMD等国际巨头的竞争中迈出了重要一步。
    据悉，这款国产CPU在日常办公应用场景下表现稳定。
    """
    
    print("🚀 测试工作版本的Gemini客户端")
    print("=" * 60)
    
    result = client.generate_summary(test_content, "test_news.md")
    
    print("=" * 60)
    print("📊 测试结果:")
    print(f"处理状态: {result.processing_status}")
    print(f"标题: {result.title}")
    print(f"核心事实: {result.core_facts}")
    print(f"关键点: {result.key_points}")
    print(f"重要性: {result.importance_level}")
    print(f"关键词: {result.keywords}")
    
    if result.error_message:
        print(f"错误信息: {result.error_message}")
    
    return result.processing_status == "success"

if __name__ == "__main__":
    success = main()
    print(f"\n测试结果: {'✅ 成功' if success else '❌ 失败'}")