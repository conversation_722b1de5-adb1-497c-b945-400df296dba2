#!/usr/bin/env python3
"""
Gemini API客户端模块

提供与Google Gemini API的交互功能，包括：
- API调用和响应处理
- 错误处理和重试机制
- 内容类型检测
- 长文档分块处理
- 提示词模板管理
"""

import time
import logging
import re
from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold


class ContentType(Enum):
    """内容类型枚举"""
    NEWS = "news"
    TECH_ARTICLE = "tech_article"
    GENERAL_ARTICLE = "general_article"
    UNKNOWN = "unknown"


@dataclass
class SummaryResponse:
    """汇总响应数据类"""
    title: str
    core_facts: str
    key_points: List[str]
    importance_level: str
    importance_reason: str
    keywords: List[str]
    content_type: str
    processing_status: str
    error_message: Optional[str] = None


class ContentTypeDetector:
    """内容类型检测器"""
    
    @staticmethod
    def detect_type(content: str, filename: str) -> ContentType:
        """
        检测内容类型
        
        Args:
            content: 文档内容
            filename: 文件名
            
        Returns:
            ContentType: 检测到的内容类型
        """
        content_lower = content.lower()
        filename_lower = filename.lower()
        
        # 新闻关键词
        news_keywords = [
            '新闻', '报道', '消息', '据悉', '记者', '采访', '发布会',
            'news', 'report', 'breaking', 'journalist', 'press',
            '今日', '昨日', '本周', '上月', '最新', '突发'
        ]
        
        # 技术文章关键词
        tech_keywords = [
            'python', 'javascript', 'api', '算法', '编程', '开发',
            'github', 'code', 'function', 'class', 'import',
            '技术', '代码', '框架', '库', '工具', '教程'
        ]
        
        # 计算关键词匹配分数
        news_score = sum(1 for keyword in news_keywords if keyword in content_lower)
        tech_score = sum(1 for keyword in tech_keywords if keyword in content_lower)
        
        # 文件名检测
        if any(keyword in filename_lower for keyword in ['news', '新闻', 'report']):
            news_score += 2
        if any(keyword in filename_lower for keyword in ['tech', '技术', 'code', 'dev']):
            tech_score += 2
        
        # 根据分数判断类型
        if news_score > tech_score and news_score >= 2:
            return ContentType.NEWS
        elif tech_score > news_score and tech_score >= 2:
            return ContentType.TECH_ARTICLE
        elif news_score > 0 or tech_score > 0:
            return ContentType.GENERAL_ARTICLE
        else:
            return ContentType.UNKNOWN


class PromptTemplateManager:
    """提示词模板管理器"""
    
    TEMPLATES = {
        ContentType.NEWS: """
作为专业新闻汇总助手，请对以下新闻内容进行结构化汇总。

请按照以下JSON格式返回结果：
{
    "title": "新闻标题",
    "core_facts": "核心事实（包含5W1H：时间、地点、人物、事件、原因、影响）",
    "key_points": ["要点1", "要点2", "要点3"],
    "importance_level": "高/中/低",
    "importance_reason": "重要性评估理由",
    "keywords": ["关键词1", "关键词2", "关键词3"]
}

汇总要求：
1. 提取核心事实，包含时间、地点、人物、事件、原因、影响等要素
2. 突出3-5个关键信息点
3. 评估新闻重要性等级（高/中/低）并说明理由
4. 生成3-5个相关关键词标签
5. 保持客观中立，避免主观判断

新闻内容：
{content}
""",
        
        ContentType.TECH_ARTICLE: """
作为技术文章汇总助手，请对以下技术内容进行结构化汇总。

请按照以下JSON格式返回结果：
{
    "title": "文章标题",
    "core_facts": "核心技术概念和主题",
    "key_points": ["技术要点1", "技术要点2", "技术要点3"],
    "importance_level": "高/中/低",
    "importance_reason": "技术重要性和实用性评估",
    "keywords": ["技术标签1", "技术标签2", "技术标签3"]
}

汇总要求：
1. 提取核心技术概念和主题
2. 总结主要方法、解决方案或技术要点
3. 突出关键结论、发现或最佳实践
4. 评估技术重要性和实用性
5. 生成相关技术标签

技术内容：
{content}
""",
        
        ContentType.GENERAL_ARTICLE: """
作为通用文章汇总助手，请对以下内容进行结构化汇总。

请按照以下JSON格式返回结果：
{
    "title": "文章标题",
    "core_facts": "文章主要观点和核心内容",
    "key_points": ["要点1", "要点2", "要点3"],
    "importance_level": "高/中/低",
    "importance_reason": "内容价值和重要性评估",
    "keywords": ["主题标签1", "主题标签2", "主题标签3"]
}

汇总要求：
1. 提取文章主要观点和核心论述
2. 总结核心论据或关键要点
3. 评估内容价值和重要性
4. 生成相关主题标签
5. 保持客观中立的语调

文章内容：
{content}
"""
    }
    
    @classmethod
    def get_prompt(cls, content_type: ContentType, content: str) -> str:
        """
        获取指定内容类型的提示词
        
        Args:
            content_type: 内容类型
            content: 文档内容
            
        Returns:
            str: 格式化的提示词
        """
        template = cls.TEMPLATES.get(content_type, cls.TEMPLATES[ContentType.GENERAL_ARTICLE])
        return template.format(content=content)


class GeminiClient:
    """Gemini API客户端"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp", 
                 retry_attempts: int = 3, retry_delay: int = 5):
        """
        初始化Gemini客户端
        
        Args:
            api_key: API密钥
            model_name: 模型名称
            retry_attempts: 重试次数
            retry_delay: 重试延迟（秒）
        """
        self.api_key = api_key
        self.model_name = model_name
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.logger = logging.getLogger(__name__)
        
        # 配置Gemini API
        genai.configure(api_key=api_key)
        
        # 初始化模型
        self.model = genai.GenerativeModel(
            model_name=model_name,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )
        
        self.content_detector = ContentTypeDetector()
        self.prompt_manager = PromptTemplateManager()
    
    def generate_summary(self, content: str, filename: str = "") -> SummaryResponse:
        """
        生成内容汇总
        
        Args:
            content: 文档内容
            filename: 文件名（用于类型检测）
            
        Returns:
            SummaryResponse: 汇总结果
        """
        try:
            # 检测内容类型
            content_type = self.content_detector.detect_type(content, filename)
            self.logger.info(f"检测到内容类型: {content_type.value}")
            
            # 检查内容长度，如果太长则分块处理
            if len(content) > 100000:  # 10万字符
                return self._process_long_content(content, content_type, filename)
            
            # 生成提示词
            prompt = self.prompt_manager.get_prompt(content_type, content)
            
            # 调用API
            response_text = self._call_api_with_retry(prompt)
            
            # 解析响应
            summary = self._parse_response(response_text, content_type)
            summary.processing_status = "success"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"生成汇总失败: {e}")
            return SummaryResponse(
                title="处理失败",
                core_facts="",
                key_points=[],
                importance_level="低",
                importance_reason="处理过程中发生错误",
                keywords=[],
                content_type=ContentType.UNKNOWN.value,
                processing_status="error",
                error_message=str(e)
            )
    
    def _call_api_with_retry(self, prompt: str) -> str:
        """
        带重试机制的API调用
        
        Args:
            prompt: 提示词
            
        Returns:
            str: API响应文本
        """
        last_error = None
        
        for attempt in range(self.retry_attempts):
            try:
                self.logger.debug(f"API调用尝试 {attempt + 1}/{self.retry_attempts}")
                
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.3,
                        max_output_tokens=2048,
                    )
                )
                
                if response.text:
                    return response.text
                else:
                    raise Exception("API返回空响应")
                    
            except Exception as e:
                last_error = e
                self.logger.warning(f"API调用失败 (尝试 {attempt + 1}): {e}")
                
                # 如果是速率限制错误，等待更长时间
                if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                    wait_time = self.retry_delay * (2 ** attempt)  # 指数退避
                    self.logger.info(f"遇到速率限制，等待 {wait_time} 秒")
                    time.sleep(wait_time)
                else:
                    time.sleep(self.retry_delay)
        
        raise Exception(f"API调用失败，已重试 {self.retry_attempts} 次: {last_error}")
    
    def _process_long_content(self, content: str, content_type: ContentType, filename: str) -> SummaryResponse:
        """
        处理长文档（分块汇总）
        
        Args:
            content: 文档内容
            content_type: 内容类型
            filename: 文件名
            
        Returns:
            SummaryResponse: 汇总结果
        """
        self.logger.info("文档过长，启用分块处理")
        
        # 将内容分块
        chunks = self._chunk_content(content, 50000)  # 5万字符一块
        chunk_summaries = []
        
        for i, chunk in enumerate(chunks):
            self.logger.info(f"处理分块 {i + 1}/{len(chunks)}")
            
            # 为分块生成简化的提示词
            chunk_prompt = f"""
请简要汇总以下内容的核心要点（这是第{i+1}部分，共{len(chunks)}部分）：

{chunk}

请提供：
1. 主要内容概述
2. 关键信息点（2-3个）
3. 重要关键词（2-3个）
"""
            
            try:
                chunk_response = self._call_api_with_retry(chunk_prompt)
                chunk_summaries.append(chunk_response)
            except Exception as e:
                self.logger.error(f"分块 {i + 1} 处理失败: {e}")
                chunk_summaries.append(f"分块 {i + 1} 处理失败: {str(e)}")
        
        # 合并分块汇总
        combined_summary = "\n\n".join(chunk_summaries)
        
        # 对合并后的汇总再次处理
        final_prompt = self.prompt_manager.get_prompt(content_type, combined_summary)
        final_response = self._call_api_with_retry(final_prompt)
        
        summary = self._parse_response(final_response, content_type)
        summary.processing_status = "success_chunked"
        
        return summary
    
    def _chunk_content(self, content: str, chunk_size: int) -> List[str]:
        """
        将内容分块
        
        Args:
            content: 原始内容
            chunk_size: 分块大小
            
        Returns:
            List[str]: 分块列表
        """
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            
            # 尝试在句号或段落处分割
            if end < len(content):
                # 寻找最近的句号或换行符
                for i in range(end, max(start + chunk_size // 2, end - 1000), -1):
                    if content[i] in '。\n':
                        end = i + 1
                        break
            
            chunks.append(content[start:end])
            start = end
        
        return chunks
    
    def _parse_response(self, response_text: str, content_type: ContentType) -> SummaryResponse:
        """
        解析API响应
        
        Args:
            response_text: API响应文本
            content_type: 内容类型
            
        Returns:
            SummaryResponse: 解析后的汇总结果
        """
        try:
            import json
            
            # 首先尝试去除markdown代码块标记
            cleaned_text = response_text.strip()
            
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]  # 去除```json
                
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]  # 去除```
                
            cleaned_text = cleaned_text.strip()
            
            # 尝试直接解析清理后的文本
            try:
                data = json.loads(cleaned_text)
            except Exception:
                # 如果直接解析失败，尝试提取JSON部分
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    data = json.loads(json_str)
                else:
                    raise ValueError("无法找到有效的JSON格式")
            
            return SummaryResponse(
                title=data.get("title", "未知标题"),
                core_facts=data.get("core_facts", ""),
                key_points=data.get("key_points", []),
                importance_level=data.get("importance_level", "中"),
                importance_reason=data.get("importance_reason", ""),
                keywords=data.get("keywords", []),
                content_type=content_type.value,
                processing_status="success"
            )
                
        except Exception as e:
            self.logger.warning(f"JSON解析失败，尝试文本解析: {e}")
            return self._parse_text_response(response_text, content_type)
    
    def _parse_text_response(self, response_text: str, content_type: ContentType) -> SummaryResponse:
        """
        解析文本格式的响应
        
        Args:
            response_text: 响应文本
            content_type: 内容类型
            
        Returns:
            SummaryResponse: 解析后的汇总结果
        """
        # 简单的文本解析逻辑
        lines = response_text.split('\n')
        
        title = "文档汇总"
        core_facts = response_text[:200] + "..." if len(response_text) > 200 else response_text
        key_points = [line.strip() for line in lines if line.strip() and len(line.strip()) > 10][:3]
        importance_level = "中"
        importance_reason = "基于内容分析"
        keywords = []
        
        # 尝试提取关键词
        for line in lines:
            if "关键词" in line or "keywords" in line.lower():
                keywords = [word.strip() for word in line.split("：")[-1].split(",")][:5]
                break
        
        if not keywords:
            keywords = ["文档", "汇总", "内容"]
        
        return SummaryResponse(
            title=title,
            core_facts=core_facts,
            key_points=key_points,
            importance_level=importance_level,
            importance_reason=importance_reason,
            keywords=keywords,
            content_type=content_type.value,
            processing_status="success_text_parsed"
        )
