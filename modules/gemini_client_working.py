#!/usr/bin/env python3
"""
工作版本的Gemini API客户端模块
"""

import time
import logging
import re
import json
from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold


class ContentType(Enum):
    """内容类型枚举"""
    NEWS = "news"
    TECH_ARTICLE = "tech_article"
    GENERAL_ARTICLE = "general_article"
    UNKNOWN = "unknown"


@dataclass
class SummaryResponse:
    """汇总响应数据类"""
    title: str
    core_facts: str
    key_points: List[str]
    importance_level: str
    importance_reason: str
    keywords: List[str]
    content_type: str
    processing_status: str
    error_message: Optional[str] = None


class ContentTypeDetector:
    """内容类型检测器"""
    
    @staticmethod
    def detect_type(content: str, filename: str) -> ContentType:
        """检测内容类型"""
        content_lower = content.lower()
        filename_lower = filename.lower()
        
        # 新闻关键词
        news_keywords = [
            '新闻', '报道', '消息', '据悉', '记者', '采访', '发布会',
            'news', 'report', 'breaking', 'journalist', 'press',
            '今日', '昨日', '本周', '上月', '最新', '突发'
        ]
        
        # 技术文章关键词
        tech_keywords = [
            'python', 'javascript', 'api', '算法', '编程', '开发',
            'github', 'code', 'function', 'class', 'import',
            '技术', '代码', '框架', '库', '工具', '教程'
        ]
        
        # 计算关键词匹配分数
        news_score = sum(1 for keyword in news_keywords if keyword in content_lower)
        tech_score = sum(1 for keyword in tech_keywords if keyword in content_lower)
        
        # 文件名检测
        if any(keyword in filename_lower for keyword in ['news', '新闻', 'report']):
            news_score += 2
        if any(keyword in filename_lower for keyword in ['tech', '技术', 'code', 'dev']):
            tech_score += 2
        
        # 根据分数判断类型
        if news_score > tech_score and news_score >= 2:
            return ContentType.NEWS
        elif tech_score > news_score and tech_score >= 2:
            return ContentType.TECH_ARTICLE
        elif news_score > 0 or tech_score > 0:
            return ContentType.GENERAL_ARTICLE
        else:
            return ContentType.UNKNOWN


class PromptTemplateManager:
    """提示词模板管理器"""
    
    TEMPLATES = {
        ContentType.NEWS: """
作为专业新闻汇总助手，请对以下新闻内容进行结构化汇总。

请按照以下JSON格式返回结果：
{
    "title": "新闻标题",
    "core_facts": "核心事实",
    "key_points": ["要点1", "要点2", "要点3"],
    "importance_level": "中",
    "importance_reason": "重要性评估理由",
    "keywords": ["关键词1", "关键词2", "关键词3"]
}

新闻内容：
{content}
""",
        
        ContentType.TECH_ARTICLE: """
作为技术文章汇总助手，请对以下技术内容进行结构化汇总。

请按照以下JSON格式返回结果：
{
    "title": "文章标题",
    "core_facts": "核心技术概念和主题",
    "key_points": ["技术要点1", "技术要点2", "技术要点3"],
    "importance_level": "中",
    "importance_reason": "技术重要性和实用性评估",
    "keywords": ["技术标签1", "技术标签2", "技术标签3"]
}

技术内容：
{content}
""",
        
        ContentType.GENERAL_ARTICLE: """
作为通用文章汇总助手，请对以下内容进行结构化汇总。

请按照以下JSON格式返回结果：
{
    "title": "文章标题",
    "core_facts": "文章主要观点和核心内容",
    "key_points": ["要点1", "要点2", "要点3"],
    "importance_level": "中",
    "importance_reason": "内容价值和重要性评估",
    "keywords": ["主题标签1", "主题标签2", "主题标签3"]
}

文章内容：
{content}
"""
    }
    
    @classmethod
    def get_prompt(cls, content_type: ContentType, content: str) -> str:
        """获取指定内容类型的提示词"""
        template = cls.TEMPLATES.get(content_type, cls.TEMPLATES[ContentType.GENERAL_ARTICLE])
        return template.format(content=content)


class GeminiClient:
    """Gemini API客户端 - 工作版本"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp", 
                 retry_attempts: int = 3, retry_delay: int = 5):
        """初始化Gemini客户端"""
        self.api_key = api_key
        self.model_name = model_name
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.logger = logging.getLogger(__name__)
        
        # 配置Gemini API
        genai.configure(api_key=api_key)
        
        # 初始化模型
        self.model = genai.GenerativeModel(
            model_name=model_name,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )
        
        self.content_detector = ContentTypeDetector()
        self.prompt_manager = PromptTemplateManager()
    
    def generate_summary(self, content: str, filename: str = "") -> SummaryResponse:
        """生成内容汇总"""
        try:
            # 检测内容类型
            content_type = self.content_detector.detect_type(content, filename)
            self.logger.info(f"检测到内容类型: {content_type.value}")
            
            # 生成提示词
            prompt = self.prompt_manager.get_prompt(content_type, content)
            
            # 调用API
            response_text = self._call_api_with_retry(prompt)
            
            # 解析响应
            summary = self._parse_response_safe(response_text, content_type)
            if summary.processing_status != "error":
                summary.processing_status = "success"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"生成汇总失败: {e}")
            return SummaryResponse(
                title="处理失败",
                core_facts="",
                key_points=[],
                importance_level="低",
                importance_reason="处理过程中发生错误",
                keywords=[],
                content_type=ContentType.UNKNOWN.value,
                processing_status="error",
                error_message=str(e)
            )
    
    def _call_api_with_retry(self, prompt: str) -> str:
        """带重试机制的API调用"""
        last_error = None
        
        for attempt in range(self.retry_attempts):
            try:
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.3,
                        max_output_tokens=2048,
                    )
                )
                
                if response.text:
                    return response.text
                else:
                    raise Exception("API返回空响应")
                    
            except Exception as e:
                last_error = e
                self.logger.warning(f"API调用失败 (尝试 {attempt + 1}): {e}")
                
                if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                    wait_time = self.retry_delay * (2 ** attempt)
                    self.logger.info(f"遇到速率限制，等待 {wait_time} 秒")
                    time.sleep(wait_time)
                else:
                    time.sleep(self.retry_delay)
        
        raise Exception(f"API调用失败，已重试 {self.retry_attempts} 次: {last_error}")
    
    def _parse_response_safe(self, response_text: str, content_type: ContentType) -> SummaryResponse:
        """安全的响应解析方法"""
        try:
            self.logger.debug(f"开始解析响应，长度: {len(response_text)}")
            
            # 步骤1：清理markdown代码块
            cleaned = response_text.strip()
            
            # 去除markdown代码块标记
            if cleaned.startswith('```json'):
                cleaned = cleaned[7:].strip()
            elif cleaned.startswith('```'):
                cleaned = cleaned[3:].strip()
                
            if cleaned.endswith('```'):
                cleaned = cleaned[:-3].strip()
            
            self.logger.debug(f"清理后长度: {len(cleaned)}")
            
            # 步骤2：尝试解析JSON
            try:
                data = json.loads(cleaned)
                self.logger.debug("JSON解析成功")
            except json.JSONDecodeError as e:
                self.logger.debug(f"直接解析失败: {e}")
                # 使用正则表达式提取JSON
                json_pattern = re.compile(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', re.DOTALL)
                matches = json_pattern.findall(response_text)
                
                if matches:
                    # 尝试解析找到的JSON
                    for match in matches:
                        try:
                            data = json.loads(match)
                            self.logger.debug("正则提取JSON解析成功")
                            break
                        except json.JSONDecodeError:
                            continue
                    else:
                        raise ValueError("所有JSON候选都无法解析")
                else:
                    raise ValueError("未找到有效的JSON结构")
            
            # 步骤3：构建结果
            result = SummaryResponse(
                title=data.get("title", "未知标题"),
                core_facts=data.get("core_facts", ""),
                key_points=data.get("key_points", []),
                importance_level=data.get("importance_level", "中"),
                importance_reason=data.get("importance_reason", ""),
                keywords=data.get("keywords", []),
                content_type=content_type.value,
                processing_status="success"
            )
            
            self.logger.debug(f"解析完成: {result.title}")
            return result
            
        except Exception as e:
            self.logger.error(f"解析失败: {e}")
            self.logger.debug(f"原始响应: {repr(response_text[:500])}")
            
            # 返回基于文本解析的结果
            return SummaryResponse(
                title="文档汇总",
                core_facts=response_text[:200] + "..." if len(response_text) > 200 else response_text,
                key_points=["解析失败，使用原始文本"],
                importance_level="中",
                importance_reason="JSON解析失败",
                keywords=["文档", "汇总"],
                content_type=content_type.value,
                processing_status="error",
                error_message=f"解析失败: {str(e)}"
            )