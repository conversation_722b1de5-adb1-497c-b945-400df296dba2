#!/usr/bin/env python3
"""
内容汇总处理器模块

提供核心的内容汇总处理功能，包括：
- 单文件处理
- 批量文件处理
- Markdown内容解析
- 错误处理和恢复
- 进度跟踪
"""

import logging
import markdown
import re
from pathlib import Path
from typing import List, Optional, Callable, Dict, Any
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time

from .gemini_client import GeminiClient, SummaryResponse
from .storage import Storage, SummaryResult


class ContentSummarizer:
    """
    内容汇总处理器
    
    核心业务逻辑类，负责协调文件处理的整个流程。
    支持单文件处理和批量处理，具备完整的错误处理和进度跟踪功能。
    
    主要功能：
    1. 单文件处理 - 读取、解析、汇总单个Markdown文件
    2. 批量处理 - 并发处理多个文件，支持进度跟踪
    3. 内容解析 - 处理Markdown格式，提取纯文本内容
    4. 去重检查 - 避免重复处理已经汇总过的文件
    5. 错误恢复 - 处理各种异常情况，确保系统稳定性
    6. 统计跟踪 - 记录处理统计信息和性能指标
    
    处理流程：
    文件输入 -> 去重检查 -> 内容读取 -> Markdown解析 -> AI汇总 -> 结果存储
    
    Attributes:
        gemini_client (GeminiClient): AI客户端实例
        storage (Storage): 存储管理器实例
        max_concurrent_files (int): 最大并发处理文件数
        logger (logging.Logger): 日志记录器
        _progress_callback: 进度回调函数
        _processing_stats (Dict): 处理统计信息
    """
    
    def __init__(self, gemini_client: GeminiClient, storage: Storage, 
                 max_concurrent_files: int = 5):
        """
        初始化内容汇总处理器
        
        Args:
            gemini_client: Gemini API客户端
            storage: 存储管理器
            max_concurrent_files: 最大并发处理文件数
        """
        self.gemini_client = gemini_client
        self.storage = storage
        self.max_concurrent_files = max_concurrent_files
        self.logger = logging.getLogger(__name__)
        
        # 进度跟踪
        self._progress_callback = None
        self._current_progress = 0
        self._total_files = 0
        self._processed_files = 0
        self._failed_files = 0
        
        # 线程安全锁
        self._progress_lock = threading.Lock()
        
        # 处理统计
        self._processing_stats = {
            'start_time': None,
            'end_time': None,
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'total_processing_time': 0
        }
    
    def set_progress_callback(self, callback: Callable[[int, int, str], None]) -> None:
        """
        设置进度回调函数
        
        Args:
            callback: 回调函数，参数为(current, total, status)
        """
        self._progress_callback = callback
    
    def process_file(self, file_path: Path) -> SummaryResult:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            SummaryResult: 汇总结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始处理文件: {file_path}")
            
            # 检查文件是否已处理
            if self.storage.processed_tracker.is_processed(file_path):
                self.logger.info(f"文件已处理，跳过: {file_path}")
                return self._create_skipped_result(file_path, "文件已处理")
            
            # 提取文件内容
            content = self._extract_content(file_path)
            if not content:
                raise ValueError("文件内容为空或无法读取")
            
            # 验证Markdown格式
            if not self._validate_markdown(content):
                self.logger.warning(f"Markdown格式可能有问题: {file_path}")
            
            # 调用Gemini API生成汇总
            api_response = self.gemini_client.generate_summary(content, file_path.name)
            
            # 创建汇总结果
            summary_result = self._create_summary_result(file_path, api_response, start_time)
            
            # 保存结果
            if self.storage.save_summary(summary_result):
                self.logger.info(f"文件处理完成: {file_path}")
                return summary_result
            else:
                raise Exception("保存汇总结果失败")
                
        except Exception as e:
            self.logger.error(f"处理文件失败: {file_path}, {e}")
            return self._create_error_result(file_path, str(e), start_time)
    
    def process_batch(self, file_paths: List[Path], 
                     progress_callback: Optional[Callable[[int, int, str], None]] = None) -> List[SummaryResult]:
        """
        批量处理文件
        
        Args:
            file_paths: 文件路径列表
            progress_callback: 进度回调函数
            
        Returns:
            List[SummaryResult]: 汇总结果列表
        """
        if progress_callback:
            self.set_progress_callback(progress_callback)
        
        self._reset_progress(len(file_paths))
        self._processing_stats['start_time'] = datetime.now()
        
        results = []
        
        try:
            self.logger.info(f"开始批量处理 {len(file_paths)} 个文件")
            
            # 使用线程池并发处理
            with ThreadPoolExecutor(max_workers=self.max_concurrent_files) as executor:
                # 提交所有任务
                future_to_path = {
                    executor.submit(self._process_file_with_progress, file_path): file_path
                    for file_path in file_paths
                }
                
                # 收集结果
                for future in as_completed(future_to_path):
                    file_path = future_to_path[future]
                    try:
                        result = future.result()
                        results.append(result)
                        
                        # 更新统计
                        if result.processing_status.startswith('success'):
                            self._processing_stats['successful_files'] += 1
                        elif result.processing_status == 'skipped':
                            self._processing_stats['skipped_files'] += 1
                        else:
                            self._processing_stats['failed_files'] += 1
                            
                    except Exception as e:
                        self.logger.error(f"处理文件异常: {file_path}, {e}")
                        error_result = self._create_error_result(file_path, str(e))
                        results.append(error_result)
                        self._processing_stats['failed_files'] += 1
            
            self._processing_stats['end_time'] = datetime.now()
            self._processing_stats['total_files'] = len(file_paths)
            
            self.logger.info(f"批量处理完成: 成功 {self._processing_stats['successful_files']}, "
                           f"失败 {self._processing_stats['failed_files']}, "
                           f"跳过 {self._processing_stats['skipped_files']}")
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
        
        return results
    
    def _process_file_with_progress(self, file_path: Path) -> SummaryResult:
        """带进度更新的文件处理"""
        result = self.process_file(file_path)
        
        # 更新进度
        with self._progress_lock:
            self._processed_files += 1
            if self._progress_callback:
                self._progress_callback(
                    self._processed_files, 
                    self._total_files, 
                    f"处理完成: {file_path.name}"
                )
        
        return result
    
    def _extract_content(self, file_path: Path) -> str:
        """
        提取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件内容
        """
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    
                    # 检查内容是否合理
                    if content and len(content.strip()) > 0:
                        self.logger.debug(f"使用编码 {encoding} 成功读取文件: {file_path}")
                        return content.strip()
                        
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("无法使用任何编码读取文件")
            
        except Exception as e:
            self.logger.error(f"提取文件内容失败: {file_path}, {e}")
            return ""
    
    def _validate_markdown(self, content: str) -> bool:
        """
        验证Markdown格式
        
        Args:
            content: 文档内容
            
        Returns:
            bool: 格式是否有效
        """
        try:
            # 基本的Markdown格式检查
            if not content:
                return False
            
            # 检查是否包含基本的文本内容
            text_content = re.sub(r'[#*`\[\]()_-]', '', content)
            if len(text_content.strip()) < 10:
                return False
            
            # 尝试解析Markdown
            md = markdown.Markdown()
            html = md.convert(content)
            
            # 检查解析结果是否合理
            if len(html.strip()) < len(content.strip()) * 0.5:
                self.logger.warning("Markdown解析结果异常短")
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Markdown验证失败: {e}")
            return False
    
    def _create_summary_result(self, file_path: Path, api_response: SummaryResponse, 
                              start_time: float) -> SummaryResult:
        """
        创建汇总结果
        
        Args:
            file_path: 文件路径
            api_response: API响应
            start_time: 开始时间
            
        Returns:
            SummaryResult: 汇总结果
        """
        try:
            file_stat = file_path.stat()
            processing_time = time.time() - start_time
            
            return SummaryResult(
                file_path=str(file_path.absolute()),
                title=api_response.title,
                core_facts=api_response.core_facts,
                key_points=api_response.key_points,
                importance_level=api_response.importance_level,
                importance_reason=api_response.importance_reason,
                keywords=api_response.keywords,
                content_type=api_response.content_type,
                processing_status=api_response.processing_status,
                summary_time=datetime.now().isoformat(),
                file_size=file_stat.st_size,
                file_hash=self.storage.processed_tracker.get_file_hash(file_path),
                error_message=api_response.error_message
            )
            
        except Exception as e:
            self.logger.error(f"创建汇总结果失败: {e}")
            return self._create_error_result(file_path, f"创建结果失败: {e}", start_time)
    
    def _create_error_result(self, file_path: Path, error_message: str, 
                           start_time: float = None) -> SummaryResult:
        """
        创建错误结果
        
        Args:
            file_path: 文件路径
            error_message: 错误信息
            start_time: 开始时间
            
        Returns:
            SummaryResult: 错误结果
        """
        try:
            file_stat = file_path.stat() if file_path.exists() else None
            
            return SummaryResult(
                file_path=str(file_path.absolute()),
                title=f"处理失败: {file_path.name}",
                core_facts="",
                key_points=[],
                importance_level="低",
                importance_reason="处理失败",
                keywords=["错误", "失败"],
                content_type="unknown",
                processing_status="error",
                summary_time=datetime.now().isoformat(),
                file_size=file_stat.st_size if file_stat else 0,
                file_hash="",
                error_message=error_message
            )
            
        except Exception as e:
            self.logger.error(f"创建错误结果失败: {e}")
            # 返回最基本的错误结果
            return SummaryResult(
                file_path=str(file_path),
                title="严重错误",
                core_facts="",
                key_points=[],
                importance_level="低",
                importance_reason="严重错误",
                keywords=["错误"],
                content_type="unknown",
                processing_status="critical_error",
                summary_time=datetime.now().isoformat(),
                file_size=0,
                file_hash="",
                error_message=f"原始错误: {error_message}, 创建错误结果失败: {e}"
            )
    
    def _create_skipped_result(self, file_path: Path, reason: str) -> SummaryResult:
        """
        创建跳过结果
        
        Args:
            file_path: 文件路径
            reason: 跳过原因
            
        Returns:
            SummaryResult: 跳过结果
        """
        return SummaryResult(
            file_path=str(file_path.absolute()),
            title=f"跳过: {file_path.name}",
            core_facts="",
            key_points=[],
            importance_level="低",
            importance_reason=reason,
            keywords=["跳过"],
            content_type="unknown",
            processing_status="skipped",
            summary_time=datetime.now().isoformat(),
            file_size=file_path.stat().st_size if file_path.exists() else 0,
            file_hash="",
            error_message=reason
        )
    
    def _reset_progress(self, total_files: int) -> None:
        """重置进度计数器"""
        with self._progress_lock:
            self._total_files = total_files
            self._processed_files = 0
            self._failed_files = 0
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self._processing_stats.copy()
        
        if stats['start_time'] and stats['end_time']:
            duration = stats['end_time'] - stats['start_time']
            stats['processing_duration_seconds'] = duration.total_seconds()
            stats['files_per_minute'] = (stats['total_files'] / duration.total_seconds() * 60) if duration.total_seconds() > 0 else 0
        
        return stats


if __name__ == "__main__":
    # 测试内容汇总处理器
    from .gemini_client import GeminiClient
    from .storage import Storage
    
    # 创建测试组件
    gemini_client = GeminiClient("test_api_key")
    storage = Storage("test_output", "test_processed.json")
    summarizer = ContentSummarizer(gemini_client, storage)
    
    # 创建测试文件
    test_file = Path("test_content.md")
    test_file.write_text("""
# 测试新闻

这是一个测试新闻内容。

## 主要内容

- 测试要点1
- 测试要点2
- 测试要点3

今天发生了一件重要的事情...
""")
    
    try:
        # 测试单文件处理
        result = summarizer.process_file(test_file)
        print(f"处理结果: {result.processing_status}")
        print(f"标题: {result.title}")
        
        # 清理测试文件
        test_file.unlink()
        
    except Exception as e:
        print(f"测试失败: {e}")
        if test_file.exists():
            test_file.unlink()
