#!/usr/bin/env python3
"""
文件监控模块

提供文件系统监控功能，包括：
- 实时文件变化监控
- 定时目录扫描
- 文件过滤和验证
- 事件处理和回调
"""

import os
import time
import logging
import threading
from pathlib import Path
from typing import Callable, List, Set, Optional
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent
import schedule


class FileChangeHandler(FileSystemEventHandler):
    """
    文件变化事件处理器
    
    继承自watchdog的FileSystemEventHandler，负责处理文件系统事件。
    实现了防重复处理机制和智能文件过滤，确保只处理相关的文件变化。
    
    主要功能：
    1. 事件过滤 - 只处理符合条件的文件（扩展名、隐藏文件等）
    2. 防重复处理 - 短时间内的重复事件将被忽略
    3. 事件分类 - 处理创建、修改、移动等不同类型的事件
    4. 回调触发 - 将有效的文件变化事件通知给上层处理器
    
    支持的事件类型：
    - on_created: 文件创建事件
    - on_modified: 文件修改事件  
    - on_moved: 文件移动事件
    
    Attributes:
        callback: 文件变化回调函数
        file_filter: 文件过滤函数
        logger: 日志记录器
        _recent_events: 最近事件缓存（防重复）
        _event_timeout: 事件超时时间（秒）
    """
    
    def __init__(self, callback: Callable[[str, str], None], file_filter: Callable[[Path], bool]):
        """
        初始化事件处理器
        
        Args:
            callback: 文件变化回调函数，参数为(file_path, event_type)
            file_filter: 文件过滤函数
        """
        super().__init__()
        self.callback = callback
        self.file_filter = file_filter
        self.logger = logging.getLogger(__name__)
        
        # 防止重复处理的缓存
        self._recent_events = {}
        self._event_timeout = 2  # 2秒内的重复事件将被忽略
    
    def on_created(self, event: FileSystemEvent) -> None:
        """处理文件创建事件"""
        if not event.is_directory:
            self._handle_file_event(event.src_path, "created")
    
    def on_modified(self, event: FileSystemEvent) -> None:
        """处理文件修改事件"""
        if not event.is_directory:
            self._handle_file_event(event.src_path, "modified")
    
    def on_moved(self, event: FileSystemEvent) -> None:
        """处理文件移动事件"""
        if not event.is_directory and hasattr(event, 'dest_path'):
            self._handle_file_event(event.dest_path, "moved")
    
    def _handle_file_event(self, file_path: str, event_type: str) -> None:
        """
        处理文件事件
        
        Args:
            file_path: 文件路径
            event_type: 事件类型
        """
        try:
            path = Path(file_path)
            
            # 检查文件是否符合过滤条件
            if not self.file_filter(path):
                return
            
            # 防止重复处理
            current_time = time.time()
            event_key = f"{file_path}_{event_type}"
            
            if event_key in self._recent_events:
                if current_time - self._recent_events[event_key] < self._event_timeout:
                    return
            
            self._recent_events[event_key] = current_time
            
            # 清理过期的事件记录
            self._cleanup_recent_events(current_time)
            
            self.logger.info(f"检测到文件{event_type}: {file_path}")
            
            # 调用回调函数
            self.callback(file_path, event_type)
            
        except Exception as e:
            self.logger.error(f"处理文件事件失败: {e}")
    
    def _cleanup_recent_events(self, current_time: float) -> None:
        """清理过期的事件记录"""
        expired_keys = [
            key for key, timestamp in self._recent_events.items()
            if current_time - timestamp > self._event_timeout * 2
        ]
        for key in expired_keys:
            del self._recent_events[key]


class FileMonitor:
    """文件监控器"""
    
    def __init__(self, watch_directory: str, file_extensions: List[str], 
                 scan_interval_minutes: int = 30, 
                 ignore_hidden_files: bool = True,
                 ignore_temp_files: bool = True):
        """
        初始化文件监控器
        
        Args:
            watch_directory: 监控目录
            file_extensions: 监控的文件扩展名列表
            scan_interval_minutes: 扫描间隔（分钟）
            ignore_hidden_files: 是否忽略隐藏文件
            ignore_temp_files: 是否忽略临时文件
        """
        self.watch_directory = Path(watch_directory)
        self.file_extensions = [ext.lower() for ext in file_extensions]
        self.scan_interval_minutes = scan_interval_minutes
        self.ignore_hidden_files = ignore_hidden_files
        self.ignore_temp_files = ignore_temp_files
        
        self.logger = logging.getLogger(__name__)
        
        # 监控状态
        self._is_monitoring = False
        self._observer = None
        self._scheduler_thread = None
        self._stop_event = threading.Event()
        
        # 回调函数
        self._file_change_callback = None
        
        # 已知文件缓存
        self._known_files = set()
        self._last_scan_time = None
        
        # 验证监控目录
        if not self.watch_directory.exists():
            raise ValueError(f"监控目录不存在: {watch_directory}")
        
        if not self.watch_directory.is_dir():
            raise ValueError(f"监控路径不是目录: {watch_directory}")
    
    def set_callback(self, callback: Callable[[str, str], None]) -> None:
        """
        设置文件变化回调函数
        
        Args:
            callback: 回调函数，参数为(file_path, event_type)
        """
        self._file_change_callback = callback
    
    def start_monitoring(self) -> None:
        """开始监控"""
        if self._is_monitoring:
            self.logger.warning("监控已经在运行中")
            return
        
        if not self._file_change_callback:
            raise ValueError("必须先设置回调函数")
        
        self.logger.info(f"开始监控目录: {self.watch_directory}")
        
        # 初始扫描
        self._initial_scan()
        
        # 启动实时监控
        self._start_real_time_monitoring()
        
        # 启动定时扫描
        self._start_scheduled_scanning()
        
        self._is_monitoring = True
        self.logger.info("文件监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        if not self._is_monitoring:
            return
        
        self.logger.info("正在停止文件监控...")
        
        # 停止实时监控
        if self._observer:
            self._observer.stop()
            self._observer.join()
            self._observer = None
        
        # 停止定时扫描
        self._stop_event.set()
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            self._scheduler_thread.join()
        
        self._is_monitoring = False
        self.logger.info("文件监控已停止")
    
    def scan_directory(self) -> List[Path]:
        """
        扫描目录，返回符合条件的文件列表
        
        Returns:
            List[Path]: 文件路径列表
        """
        valid_files = []
        
        try:
            for file_path in self.watch_directory.rglob("*"):
                if file_path.is_file() and self.is_valid_file(file_path):
                    valid_files.append(file_path)
            
            self.logger.debug(f"扫描到 {len(valid_files)} 个有效文件")
            
        except Exception as e:
            self.logger.error(f"扫描目录失败: {e}")
        
        return valid_files
    
    def is_valid_file(self, file_path: Path) -> bool:
        """
        检查文件是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否有效
        """
        try:
            # 检查文件是否存在
            if not file_path.exists() or not file_path.is_file():
                return False
            
            # 检查文件扩展名
            if self.file_extensions and file_path.suffix.lower() not in self.file_extensions:
                return False
            
            # 检查是否为隐藏文件
            if self.ignore_hidden_files and file_path.name.startswith('.'):
                return False
            
            # 检查是否为临时文件
            if self.ignore_temp_files:
                temp_patterns = ['.tmp', '.temp', '~', '.swp', '.bak']
                if any(pattern in file_path.name.lower() for pattern in temp_patterns):
                    return False
            
            # 检查文件大小（避免处理空文件或过大文件）
            file_size = file_path.stat().st_size
            if file_size == 0:
                return False
            
            # 避免处理过大的文件（超过50MB）
            if file_size > 50 * 1024 * 1024:
                self.logger.warning(f"文件过大，跳过: {file_path} ({file_size / 1024 / 1024:.1f}MB)")
                return False
            
            # 检查文件是否可读
            if not os.access(file_path, os.R_OK):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查文件有效性失败: {file_path}, {e}")
            return False
    
    def _initial_scan(self) -> None:
        """初始扫描"""
        self.logger.info("执行初始目录扫描...")
        
        valid_files = self.scan_directory()
        self._known_files = {str(f) for f in valid_files}
        self._last_scan_time = datetime.now()
        
        self.logger.info(f"初始扫描完成，发现 {len(valid_files)} 个文件")
    
    def _start_real_time_monitoring(self) -> None:
        """启动实时监控"""
        try:
            event_handler = FileChangeHandler(
                callback=self._handle_file_change,
                file_filter=self.is_valid_file
            )
            
            self._observer = Observer()
            self._observer.schedule(
                event_handler, 
                str(self.watch_directory), 
                recursive=True
            )
            self._observer.start()
            
            self.logger.info("实时文件监控已启动")
            
        except Exception as e:
            self.logger.error(f"启动实时监控失败: {e}")
    
    def _start_scheduled_scanning(self) -> None:
        """启动定时扫描"""
        def run_scheduler():
            schedule.every(self.scan_interval_minutes).minutes.do(self._scheduled_scan)
            
            while not self._stop_event.is_set():
                schedule.run_pending()
                time.sleep(1)
        
        self._scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self._scheduler_thread.start()
        
        self.logger.info(f"定时扫描已启动，间隔: {self.scan_interval_minutes} 分钟")
    
    def _scheduled_scan(self) -> None:
        """定时扫描任务"""
        self.logger.info("执行定时扫描...")
        
        try:
            current_files = {str(f) for f in self.scan_directory()}
            
            # 检查新增文件
            new_files = current_files - self._known_files
            for file_path in new_files:
                self.logger.info(f"定时扫描发现新文件: {file_path}")
                self._handle_file_change(file_path, "created")
            
            # 检查修改的文件
            for file_path in current_files & self._known_files:
                try:
                    path = Path(file_path)
                    if path.exists():
                        modified_time = datetime.fromtimestamp(path.stat().st_mtime)
                        if self._last_scan_time and modified_time > self._last_scan_time:
                            self.logger.info(f"定时扫描发现修改文件: {file_path}")
                            self._handle_file_change(file_path, "modified")
                except Exception as e:
                    self.logger.error(f"检查文件修改时间失败: {file_path}, {e}")
            
            # 更新已知文件列表和扫描时间
            self._known_files = current_files
            self._last_scan_time = datetime.now()
            
            self.logger.debug(f"定时扫描完成，当前监控 {len(current_files)} 个文件")
            
        except Exception as e:
            self.logger.error(f"定时扫描失败: {e}")
    
    def _handle_file_change(self, file_path: str, event_type: str) -> None:
        """
        处理文件变化
        
        Args:
            file_path: 文件路径
            event_type: 事件类型
        """
        try:
            if self._file_change_callback:
                self._file_change_callback(file_path, event_type)
        except Exception as e:
            self.logger.error(f"处理文件变化回调失败: {e}")
    
    def get_status(self) -> dict:
        """
        获取监控状态
        
        Returns:
            dict: 状态信息
        """
        return {
            'is_monitoring': self._is_monitoring,
            'watch_directory': str(self.watch_directory),
            'file_extensions': self.file_extensions,
            'scan_interval_minutes': self.scan_interval_minutes,
            'known_files_count': len(self._known_files),
            'last_scan_time': self._last_scan_time.isoformat() if self._last_scan_time else None
        }


if __name__ == "__main__":
    # 测试文件监控
    def test_callback(file_path: str, event_type: str):
        print(f"文件变化: {event_type} - {file_path}")
    
    # 创建测试目录
    test_dir = Path("test_monitor")
    test_dir.mkdir(exist_ok=True)
    
    try:
        monitor = FileMonitor(
            watch_directory=str(test_dir),
            file_extensions=[".md", ".txt"],
            scan_interval_minutes=1
        )
        
        monitor.set_callback(test_callback)
        monitor.start_monitoring()
        
        print("监控已启动，按Ctrl+C停止...")
        
        # 创建测试文件
        test_file = test_dir / "test.md"
        test_file.write_text("测试内容")
        
        time.sleep(5)
        
        # 修改测试文件
        test_file.write_text("修改后的内容")
        
        time.sleep(5)
        
    except KeyboardInterrupt:
        print("\n正在停止监控...")
    finally:
        monitor.stop_monitoring()
        # 清理测试文件
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)
