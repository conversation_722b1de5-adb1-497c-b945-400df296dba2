#!/usr/bin/env python3
"""
配置管理模块

提供统一的配置管理功能，支持从环境变量和配置文件加载设置。
优先级：环境变量 > 配置文件 > 默认值
"""

import os
import configparser
from dataclasses import dataclass, field
from typing import List, Optional
from pathlib import Path
import logging
from dotenv import load_dotenv


@dataclass
class Config:
    """应用配置类"""
    
    # API配置
    gemini_api_key: str = ""
    model_name: str = "gemini-2.0-flash-exp"
    temperature: float = 0.3
    max_tokens: int = 2048
    
    # 文件监控配置
    watch_directory: str = "/Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox"
    scan_interval_minutes: int = 30
    file_extensions: List[str] = field(default_factory=lambda: [".md"])
    ignore_hidden_files: bool = True
    ignore_temp_files: bool = True
    
    # 处理配置
    max_concurrent_files: int = 5
    chunk_size_chars: int = 4000
    retry_attempts: int = 3
    retry_delay_seconds: int = 5
    enable_batch_processing: bool = True
    
    # 输出配置
    output_directory: str = "data/summaries"
    processed_file_tracker: str = "data/processed.json"
    log_directory: str = "logs"
    log_level: str = "INFO"
    enable_markdown_reports: bool = True
    
    # 高级配置
    enable_real_time_monitoring: bool = True
    progress_update_interval: int = 10
    memory_optimization: bool = True
    api_rate_limit_per_minute: int = 60
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保路径是Path对象
        self.watch_directory = str(Path(self.watch_directory).expanduser())
        self.output_directory = str(Path(self.output_directory))
        self.processed_file_tracker = str(Path(self.processed_file_tracker))
        self.log_directory = str(Path(self.log_directory))
    
    @classmethod
    def load(cls, config_file: Optional[str] = None) -> "Config":
        """
        加载配置
        
        Args:
            config_file: 配置文件路径，默认为data/config.ini
            
        Returns:
            Config: 配置实例
        """
        # 加载环境变量
        load_dotenv()
        
        # 创建默认配置实例
        config = cls()
        
        # 从配置文件加载
        if config_file is None:
            config_file = "data/config.ini"
        
        config.load_from_file(config_file)
        
        # 从环境变量加载（优先级最高）
        config.load_from_env()
        
        # 验证配置
        config.validate()
        
        return config
    
    def load_from_env(self) -> None:
        """从环境变量加载配置"""
        # API配置
        if os.getenv("GEMINI_API_KEY"):
            self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        if os.getenv("MODEL_NAME"):
            self.model_name = os.getenv("MODEL_NAME")
        if os.getenv("TEMPERATURE"):
            self.temperature = float(os.getenv("TEMPERATURE"))
        if os.getenv("MAX_TOKENS"):
            self.max_tokens = int(os.getenv("MAX_TOKENS"))
            
        # 文件监控配置
        if os.getenv("WATCH_DIRECTORY"):
            self.watch_directory = os.getenv("WATCH_DIRECTORY")
        if os.getenv("SCAN_INTERVAL_MINUTES"):
            self.scan_interval_minutes = int(os.getenv("SCAN_INTERVAL_MINUTES"))
        if os.getenv("FILE_EXTENSIONS"):
            self.file_extensions = os.getenv("FILE_EXTENSIONS").split(",")
            
        # 处理配置
        if os.getenv("MAX_CONCURRENT_FILES"):
            self.max_concurrent_files = int(os.getenv("MAX_CONCURRENT_FILES"))
        if os.getenv("CHUNK_SIZE_CHARS"):
            self.chunk_size_chars = int(os.getenv("CHUNK_SIZE_CHARS"))
        if os.getenv("RETRY_ATTEMPTS"):
            self.retry_attempts = int(os.getenv("RETRY_ATTEMPTS"))
        if os.getenv("RETRY_DELAY_SECONDS"):
            self.retry_delay_seconds = int(os.getenv("RETRY_DELAY_SECONDS"))
            
        # 输出配置
        if os.getenv("OUTPUT_DIRECTORY"):
            self.output_directory = os.getenv("OUTPUT_DIRECTORY")
        if os.getenv("LOG_DIRECTORY"):
            self.log_directory = os.getenv("LOG_DIRECTORY")
        if os.getenv("LOG_LEVEL"):
            self.log_level = os.getenv("LOG_LEVEL")
            
        # 布尔值配置
        if os.getenv("ENABLE_REAL_TIME_MONITORING"):
            self.enable_real_time_monitoring = os.getenv("ENABLE_REAL_TIME_MONITORING").lower() == "true"
        if os.getenv("ENABLE_BATCH_PROCESSING"):
            self.enable_batch_processing = os.getenv("ENABLE_BATCH_PROCESSING").lower() == "true"
    
    def load_from_file(self, config_path: str) -> None:
        """
        从配置文件加载配置
        
        Args:
            config_path: 配置文件路径
        """
        config_file = Path(config_path)
        if not config_file.exists():
            logging.warning(f"配置文件不存在: {config_path}")
            return
            
        parser = configparser.ConfigParser()
        try:
            parser.read(config_path, encoding='utf-8')
            
            # API配置
            if parser.has_section('API'):
                api_section = parser['API']
                if 'model_name' in api_section:
                    self.model_name = api_section['model_name']
                if 'temperature' in api_section:
                    self.temperature = api_section.getfloat('temperature')
                if 'max_tokens' in api_section:
                    self.max_tokens = api_section.getint('max_tokens')
            
            # 监控配置
            if parser.has_section('MONITORING'):
                monitoring_section = parser['MONITORING']
                if 'watch_directory' in monitoring_section:
                    self.watch_directory = monitoring_section['watch_directory']
                if 'scan_interval_minutes' in monitoring_section:
                    self.scan_interval_minutes = monitoring_section.getint('scan_interval_minutes')
                if 'file_extensions' in monitoring_section:
                    self.file_extensions = [ext.strip() for ext in monitoring_section['file_extensions'].split(',')]
                if 'ignore_hidden_files' in monitoring_section:
                    self.ignore_hidden_files = monitoring_section.getboolean('ignore_hidden_files')
                if 'ignore_temp_files' in monitoring_section:
                    self.ignore_temp_files = monitoring_section.getboolean('ignore_temp_files')
            
            # 处理配置
            if parser.has_section('PROCESSING'):
                processing_section = parser['PROCESSING']
                if 'max_concurrent_files' in processing_section:
                    self.max_concurrent_files = processing_section.getint('max_concurrent_files')
                if 'chunk_size_chars' in processing_section:
                    self.chunk_size_chars = processing_section.getint('chunk_size_chars')
                if 'retry_attempts' in processing_section:
                    self.retry_attempts = processing_section.getint('retry_attempts')
                if 'retry_delay_seconds' in processing_section:
                    self.retry_delay_seconds = processing_section.getint('retry_delay_seconds')
                if 'enable_batch_processing' in processing_section:
                    self.enable_batch_processing = processing_section.getboolean('enable_batch_processing')
            
            # 输出配置
            if parser.has_section('OUTPUT'):
                output_section = parser['OUTPUT']
                if 'output_directory' in output_section:
                    self.output_directory = output_section['output_directory']
                if 'processed_file_tracker' in output_section:
                    self.processed_file_tracker = output_section['processed_file_tracker']
                if 'log_directory' in output_section:
                    self.log_directory = output_section['log_directory']
                if 'log_level' in output_section:
                    self.log_level = output_section['log_level']
                if 'enable_markdown_reports' in output_section:
                    self.enable_markdown_reports = output_section.getboolean('enable_markdown_reports')
            
            # 高级配置
            if parser.has_section('ADVANCED'):
                advanced_section = parser['ADVANCED']
                if 'enable_real_time_monitoring' in advanced_section:
                    self.enable_real_time_monitoring = advanced_section.getboolean('enable_real_time_monitoring')
                if 'progress_update_interval' in advanced_section:
                    self.progress_update_interval = advanced_section.getint('progress_update_interval')
                if 'memory_optimization' in advanced_section:
                    self.memory_optimization = advanced_section.getboolean('memory_optimization')
                if 'api_rate_limit_per_minute' in advanced_section:
                    self.api_rate_limit_per_minute = advanced_section.getint('api_rate_limit_per_minute')
                    
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
    
    def validate(self) -> bool:
        """
        验证配置有效性
        
        Returns:
            bool: 配置是否有效
        """
        errors = []
        
        # 验证必需的配置
        if not self.gemini_api_key:
            errors.append("GEMINI_API_KEY 未设置")
        
        # 验证目录路径
        watch_path = Path(self.watch_directory)
        if not watch_path.exists():
            errors.append(f"监控目录不存在: {self.watch_directory}")
        
        # 验证数值范围
        if self.scan_interval_minutes <= 0:
            errors.append("扫描间隔必须大于0")
        
        if self.max_concurrent_files <= 0:
            errors.append("最大并发文件数必须大于0")
        
        if self.chunk_size_chars <= 0:
            errors.append("分块大小必须大于0")
        
        if self.retry_attempts < 0:
            errors.append("重试次数不能为负数")
        
        # 验证日志级别
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.log_level.upper() not in valid_log_levels:
            errors.append(f"无效的日志级别: {self.log_level}")
        
        # 验证文件扩展名
        if not self.file_extensions:
            errors.append("文件扩展名列表不能为空")
        
        if errors:
            for error in errors:
                logging.error(f"配置验证失败: {error}")
            return False
        
        return True
    
    def create_directories(self) -> None:
        """创建必需的目录"""
        directories = [
            self.output_directory,
            self.log_directory,
            Path(self.processed_file_tracker).parent
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'api': {
                'model_name': self.model_name,
                'temperature': self.temperature,
                'max_tokens': self.max_tokens
            },
            'monitoring': {
                'watch_directory': self.watch_directory,
                'scan_interval_minutes': self.scan_interval_minutes,
                'file_extensions': self.file_extensions,
                'ignore_hidden_files': self.ignore_hidden_files,
                'ignore_temp_files': self.ignore_temp_files
            },
            'processing': {
                'max_concurrent_files': self.max_concurrent_files,
                'chunk_size_chars': self.chunk_size_chars,
                'retry_attempts': self.retry_attempts,
                'retry_delay_seconds': self.retry_delay_seconds,
                'enable_batch_processing': self.enable_batch_processing
            },
            'output': {
                'output_directory': self.output_directory,
                'processed_file_tracker': self.processed_file_tracker,
                'log_directory': self.log_directory,
                'log_level': self.log_level,
                'enable_markdown_reports': self.enable_markdown_reports
            },
            'advanced': {
                'enable_real_time_monitoring': self.enable_real_time_monitoring,
                'progress_update_interval': self.progress_update_interval,
                'memory_optimization': self.memory_optimization,
                'api_rate_limit_per_minute': self.api_rate_limit_per_minute
            }
        }


def setup_logging(config: Config) -> None:
    """
    设置日志系统
    
    Args:
        config: 配置实例
    """
    # 创建日志目录
    log_dir = Path(config.log_directory)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置日志处理器
    handlers = [
        logging.StreamHandler(),  # 控制台输出
        logging.FileHandler(
            log_dir / 'news_summarizer.log',
            encoding='utf-8'
        )  # 文件输出
    ]
    
    # 设置日志级别和格式
    logging.basicConfig(
        level=getattr(logging, config.log_level.upper()),
        format=log_format,
        handlers=handlers
    )
    
    # 设置第三方库的日志级别
    logging.getLogger('watchdog').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


if __name__ == "__main__":
    # 测试配置加载
    config = Config.load()
    print("配置加载成功:")
    print(f"监控目录: {config.watch_directory}")
    print(f"输出目录: {config.output_directory}")
    print(f"日志级别: {config.log_level}")
    print(f"API密钥已设置: {'是' if config.gemini_api_key else '否'}")
