"""
新闻摘要程序核心模块包

这个包包含了新闻摘要系统的所有核心功能模块。
每个模块都有特定的职责，通过依赖注入的方式协同工作。

模块说明：
- config: 配置管理 - 统一管理应用的所有配置参数
- file_monitor: 文件监控 - 实时监控文件系统变化和定期扫描
- gemini_client: Gemini API客户端 - 与Google Gemini AI服务通信
- summarizer: 内容汇总处理 - 核心业务逻辑，协调整个处理流程
- storage: 数据存储 - 汇总结果存储和已处理文件跟踪
- error_handler: 错误处理 - 全局错误管理和重试机制
- utils: 工具函数 - 性能监控、内存管理等通用工具

架构设计：
采用分层架构和依赖注入模式，各模块职责清晰，便于维护和测试。
- 应用层: main.py (NewsForgeApp)
- 业务层: summarizer.py (ContentSummarizer)  
- 服务层: gemini_client.py, storage.py, file_monitor.py
- 基础层: config.py, error_handler.py, utils.py
"""

__version__ = "1.0.0"
__author__ = "Jeff"

# 导入核心类，方便外部使用
from .config import Config
from .file_monitor import FileMonitor
from .gemini_client import GeminiClient
from .summarizer import ContentSummarizer
from .storage import Storage

__all__ = [
    "Config",
    "FileMonitor", 
    "GeminiClient",
    "ContentSummarizer",
    "Storage"
]
