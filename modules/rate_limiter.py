#!/usr/bin/env python3
"""
API速率限制管理模块

提供智能的API调用速率限制功能，支持：
- 每分钟请求数 (RPM) 限制
- 每天请求数 (RPD) 限制  
- 多模型自动选择
- 请求历史跟踪
- 智能等待和重试
"""

import time
import json
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)


@dataclass
class RequestRecord:
    """请求记录"""
    timestamp: float
    model: str
    success: bool


@dataclass
class ModelStatus:
    """模型状态"""
    rpm_requests: List[float] = field(default_factory=list)
    rpd_requests: List[float] = field(default_factory=list)
    last_request: float = 0
    total_requests: int = 0
    failed_requests: int = 0


class RateLimiter:
    """
    智能API速率限制器
    
    功能特性:
    1. 多模型支持 - 支持不同Gemini模型的独立限制
    2. 时间窗口管理 - 精确的分钟和天级别限制跟踪
    3. 智能模型选择 - 自动选择可用的最佳模型
    4. 持久化存储 - 跨程序运行保持请求历史
    5. 优雅等待 - 智能计算最佳等待时间
    """
    
    def __init__(self, model_limits: Dict[str, Dict], storage_path: str = "data/rate_limit.json"):
        """
        初始化速率限制器
        
        Args:
            model_limits: 模型限制配置字典
            storage_path: 请求记录存储路径
        """
        self.model_limits = model_limits
        self.storage_path = Path(storage_path)
        self.model_status: Dict[str, ModelStatus] = {}
        
        # 初始化模型状态
        for model_name in model_limits.keys():
            self.model_status[model_name] = ModelStatus()
        
        # 加载历史记录
        self._load_request_history()
        
        # 确保存储目录存在
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
    
    def _load_request_history(self) -> None:
        """从文件加载请求历史记录"""
        if not self.storage_path.exists():
            logger.info("速率限制记录文件不存在，将创建新文件")
            return
        
        try:
            with open(self.storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            current_time = time.time()
            cutoff_time = current_time - 24 * 3600  # 24小时前
            
            for model_name, records in data.get("models", {}).items():
                if model_name not in self.model_status:
                    continue
                
                status = self.model_status[model_name]
                
                # 加载最近的请求记录
                rpm_records = [ts for ts in records.get("rpm_requests", []) 
                             if ts > current_time - 60]  # 1分钟内
                rpd_records = [ts for ts in records.get("rpd_requests", []) 
                             if ts > cutoff_time]  # 24小时内
                
                status.rpm_requests = rpm_records
                status.rpd_requests = rpd_records
                status.last_request = records.get("last_request", 0)
                status.total_requests = records.get("total_requests", 0)
                status.failed_requests = records.get("failed_requests", 0)
            
            logger.info(f"已加载速率限制历史记录: {len(data.get('models', {}))} 个模型")
            
        except Exception as e:
            logger.error(f"加载速率限制记录失败: {e}")
    
    def _save_request_history(self) -> None:
        """保存请求历史记录到文件"""
        try:
            data = {
                "last_updated": datetime.now().isoformat(),
                "models": {}
            }
            
            for model_name, status in self.model_status.items():
                data["models"][model_name] = {
                    "rpm_requests": status.rpm_requests,
                    "rpd_requests": status.rpd_requests,
                    "last_request": status.last_request,
                    "total_requests": status.total_requests,
                    "failed_requests": status.failed_requests
                }
            
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"保存速率限制记录失败: {e}")
    
    def _cleanup_old_requests(self, model_name: str) -> None:
        """清理过期的请求记录"""
        if model_name not in self.model_status:
            return
        
        current_time = time.time()
        status = self.model_status[model_name]
        
        # 清理1分钟前的RPM记录
        status.rpm_requests = [ts for ts in status.rpm_requests 
                              if ts > current_time - 60]
        
        # 清理24小时前的RPD记录
        status.rpd_requests = [ts for ts in status.rpd_requests 
                              if ts > current_time - 24 * 3600]
    
    def get_model_status(self, model_name: str) -> Dict[str, any]:
        """
        获取模型当前状态
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict: 模型状态信息
        """
        if model_name not in self.model_status:
            return {}
        
        self._cleanup_old_requests(model_name)
        status = self.model_status[model_name]
        limits = self.model_limits.get(model_name, {})
        
        rpm_used = len(status.rpm_requests)
        rpd_used = len(status.rpd_requests)
        rpm_limit = limits.get("rpm", 0)
        rpd_limit = limits.get("rpd", 0)
        
        return {
            "rpm_used": rpm_used,
            "rpm_limit": rpm_limit,
            "rpm_available": max(0, rpm_limit - rpm_used),
            "rpd_used": rpd_used,
            "rpd_limit": rpd_limit,
            "rpd_available": max(0, rpd_limit - rpd_used),
            "total_requests": status.total_requests,
            "failed_requests": status.failed_requests,
            "success_rate": (status.total_requests - status.failed_requests) / max(1, status.total_requests),
            "last_request": status.last_request
        }
    
    def can_make_request(self, model_name: str) -> Tuple[bool, str]:
        """
        检查是否可以发出请求
        
        Args:
            model_name: 模型名称
            
        Returns:
            Tuple[bool, str]: (是否可以请求, 原因说明)
        """
        if model_name not in self.model_limits:
            return False, f"未知模型: {model_name}"
        
        self._cleanup_old_requests(model_name)
        status = self.model_status[model_name]
        limits = self.model_limits[model_name]
        
        rpm_used = len(status.rpm_requests)
        rpd_used = len(status.rpd_requests)
        
        # 检查RPM限制
        if rpm_used >= limits["rpm"]:
            return False, f"已达到每分钟请求限制 ({rpm_used}/{limits['rpm']})"
        
        # 检查RPD限制
        if rpd_used >= limits["rpd"]:
            return False, f"已达到每天请求限制 ({rpd_used}/{limits['rpd']})"
        
        return True, "可以发出请求"
    
    def get_best_available_model(self) -> Optional[str]:
        """
        获取当前最佳可用模型
        
        根据优先级和可用性选择模型:
        1. 优先选择优先级高的模型
        2. 确保模型有可用配额
        3. 考虑成功率
        
        Returns:
            Optional[str]: 最佳模型名称，如无可用模型则返回None
        """
        available_models = []
        
        for model_name in self.model_limits.keys():
            can_request, reason = self.can_make_request(model_name)
            if can_request:
                model_info = self.get_model_status(model_name)
                priority = self.model_limits[model_name].get("priority", 999)
                
                available_models.append({
                    "name": model_name,
                    "priority": priority,
                    "success_rate": model_info["success_rate"],
                    "rpm_available": model_info["rpm_available"],
                    "rpd_available": model_info["rpd_available"]
                })
        
        if not available_models:
            return None
        
        # 按优先级排序，然后按成功率排序
        available_models.sort(key=lambda x: (x["priority"], -x["success_rate"]))
        
        best_model = available_models[0]["name"]
        logger.info(f"选择模型: {best_model} (优先级: {available_models[0]['priority']}, "
                   f"成功率: {available_models[0]['success_rate']:.2%})")
        
        return best_model
    
    def calculate_wait_time(self, model_name: str) -> float:
        """
        计算需要等待的时间
        
        Args:
            model_name: 模型名称
            
        Returns:
            float: 等待秒数
        """
        if model_name not in self.model_status:
            return 0
        
        self._cleanup_old_requests(model_name)
        status = self.model_status[model_name]
        limits = self.model_limits[model_name]
        
        current_time = time.time()
        
        # 检查RPM限制
        if len(status.rpm_requests) >= limits["rpm"]:
            # 找到最早的请求时间，计算到1分钟后的等待时间
            oldest_request = min(status.rpm_requests)
            wait_until = oldest_request + 60
            rpm_wait = max(0, wait_until - current_time)
        else:
            rpm_wait = 0
        
        # RPD限制通常需要等待更长时间，这里简单返回RPM等待时间
        # 实际应用中可以根据需要添加更复杂的策略
        
        return rpm_wait
    
    async def wait_if_needed(self, model_name: str) -> bool:
        """
        如果需要则等待，直到可以发出请求
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 是否成功等待（True）或无法等待（False，如RPD已用完）
        """
        wait_time = self.calculate_wait_time(model_name)
        
        if wait_time > 0:
            logger.info(f"模型 {model_name} 需要等待 {wait_time:.1f} 秒")
            await asyncio.sleep(wait_time)
        
        # 再次检查是否可以发出请求
        can_request, reason = self.can_make_request(model_name)
        if not can_request:
            logger.warning(f"等待后仍无法请求 {model_name}: {reason}")
        
        return can_request
    
    def record_request(self, model_name: str, success: bool = True) -> None:
        """
        记录请求
        
        Args:
            model_name: 模型名称
            success: 请求是否成功
        """
        if model_name not in self.model_status:
            logger.warning(f"尝试记录未知模型的请求: {model_name}")
            return
        
        current_time = time.time()
        status = self.model_status[model_name]
        
        # 添加请求记录
        status.rpm_requests.append(current_time)
        status.rpd_requests.append(current_time)
        status.last_request = current_time
        status.total_requests += 1
        
        if not success:
            status.failed_requests += 1
        
        # 清理旧记录
        self._cleanup_old_requests(model_name)
        
        # 保存到文件
        self._save_request_history()
        
        logger.debug(f"记录请求: {model_name}, 成功: {success}")
    
    def get_all_status(self) -> Dict[str, Dict]:
        """获取所有模型的状态"""
        result = {}
        for model_name in self.model_limits.keys():
            result[model_name] = self.get_model_status(model_name)
        return result
    
    def reset_model_history(self, model_name: str) -> None:
        """重置特定模型的历史记录"""
        if model_name in self.model_status:
            self.model_status[model_name] = ModelStatus()
            self._save_request_history()
            logger.info(f"已重置模型 {model_name} 的历史记录")
    
    def reset_all_history(self) -> None:
        """重置所有模型的历史记录"""
        for model_name in self.model_status.keys():
            self.model_status[model_name] = ModelStatus()
        self._save_request_history()
        logger.info("已重置所有模型的历史记录")


# 便利函数
def create_rate_limiter_from_config(config) -> RateLimiter:
    """
    从配置对象创建速率限制器
    
    Args:
        config: 配置对象
        
    Returns:
        RateLimiter: 速率限制器实例
    """
    storage_path = Path(config.output_directory) / "rate_limit.json"
    return RateLimiter(config.model_limits, str(storage_path))


if __name__ == "__main__":
    # 测试用例
    import asyncio
    
    async def test_rate_limiter():
        # 测试配置
        test_limits = {
            "gemini-2.5-pro": {"rpm": 2, "rpd": 10, "priority": 1},
            "gemini-2.5-flash": {"rpm": 5, "rpd": 20, "priority": 2}
        }
        
        limiter = RateLimiter(test_limits, "test_rate_limit.json")
        
        print("=== 速率限制器测试 ===")
        
        # 测试模型选择
        best_model = limiter.get_best_available_model()
        print(f"最佳模型: {best_model}")
        
        # 测试请求记录
        for i in range(3):
            model = best_model or "gemini-2.5-flash"
            can_request, reason = limiter.can_make_request(model)
            print(f"请求 {i+1}: 模型={model}, 可请求={can_request}, 原因={reason}")
            
            if can_request:
                limiter.record_request(model, success=True)
                await asyncio.sleep(1)
            else:
                print(f"需要等待...")
                success = await limiter.wait_if_needed(model)
                if success:
                    limiter.record_request(model, success=True)
        
        # 显示状态
        print("\n=== 所有模型状态 ===")
        for model_name, status in limiter.get_all_status().items():
            print(f"{model_name}: RPM={status['rpm_used']}/{status['rpm_limit']}, "
                  f"RPD={status['rpd_used']}/{status['rpd_limit']}")
    
    asyncio.run(test_rate_limiter())