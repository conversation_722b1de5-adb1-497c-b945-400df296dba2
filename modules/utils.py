#!/usr/bin/env python3
"""
工具函数模块

提供通用的工具函数和优化功能，包括：
- 性能监控和优化
- 内存管理
- 文件操作工具
- 数据处理工具
- 系统信息获取
"""

import os
import psutil
import time
import hashlib
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from functools import wraps
import threading
import gc


class PerformanceMonitor:
    """
    性能监控器
    
    监控系统资源使用情况和应用性能指标，帮助优化系统性能和诊断问题。
    支持CPU、内存、磁盘使用率监控，以及API响应时间和文件处理时间统计。
    
    主要功能：
    1. 资源监控 - 实时监控CPU、内存、磁盘使用率
    2. 性能统计 - 记录API响应时间和文件处理时间
    3. 数据收集 - 收集历史性能数据用于分析
    4. 报告生成 - 生成性能报告和统计信息
    5. 阈值告警 - 资源使用超出阈值时发出告警
    
    使用方法：
        monitor = PerformanceMonitor()
        monitor.start_monitoring(interval=60)  # 每60秒监控一次
        # ... 运行应用 ...
        monitor.stop_monitoring()
        report = monitor.generate_report()
    
    Attributes:
        logger: 日志记录器
        metrics: 性能指标数据字典
        _monitoring: 监控状态标志
        _monitor_thread: 监控线程
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_usage': [],
            'api_response_times': [],
            'file_processing_times': []
        }
        self._monitoring = False
        self._monitor_thread = None
    
    def start_monitoring(self, interval: int = 60) -> None:
        """
        开始性能监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止性能监控"""
        self._monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join()
        self.logger.info("性能监控已停止")
    
    def _monitor_loop(self, interval: int) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.metrics['cpu_usage'].append({
                    'timestamp': datetime.now().isoformat(),
                    'value': cpu_percent
                })
                
                # 内存使用率
                memory = psutil.virtual_memory()
                self.metrics['memory_usage'].append({
                    'timestamp': datetime.now().isoformat(),
                    'value': memory.percent,
                    'available_gb': memory.available / (1024**3)
                })
                
                # 磁盘使用率
                disk = psutil.disk_usage('/')
                self.metrics['disk_usage'].append({
                    'timestamp': datetime.now().isoformat(),
                    'value': (disk.used / disk.total) * 100,
                    'free_gb': disk.free / (1024**3)
                })
                
                # 保持最近100个记录
                for key in self.metrics:
                    if len(self.metrics[key]) > 100:
                        self.metrics[key] = self.metrics[key][-100:]
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"性能监控错误: {e}")
                time.sleep(interval)
    
    def record_api_time(self, response_time: float) -> None:
        """记录API响应时间"""
        self.metrics['api_response_times'].append({
            'timestamp': datetime.now().isoformat(),
            'value': response_time
        })
        
        # 保持最近1000个记录
        if len(self.metrics['api_response_times']) > 1000:
            self.metrics['api_response_times'] = self.metrics['api_response_times'][-1000:]
    
    def record_processing_time(self, processing_time: float) -> None:
        """记录文件处理时间"""
        self.metrics['file_processing_times'].append({
            'timestamp': datetime.now().isoformat(),
            'value': processing_time
        })
        
        # 保持最近1000个记录
        if len(self.metrics['file_processing_times']) > 1000:
            self.metrics['file_processing_times'] = self.metrics['file_processing_times'][-1000:]
    
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前性能统计"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 计算平均API响应时间
            recent_api_times = [
                m['value'] for m in self.metrics['api_response_times'][-10:]
            ]
            avg_api_time = sum(recent_api_times) / len(recent_api_times) if recent_api_times else 0
            
            # 计算平均处理时间
            recent_processing_times = [
                m['value'] for m in self.metrics['file_processing_times'][-10:]
            ]
            avg_processing_time = sum(recent_processing_times) / len(recent_processing_times) if recent_processing_times else 0
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_percent': (disk.used / disk.total) * 100,
                'disk_free_gb': disk.free / (1024**3),
                'avg_api_response_time': avg_api_time,
                'avg_processing_time': avg_processing_time,
                'total_api_calls': len(self.metrics['api_response_times']),
                'total_files_processed': len(self.metrics['file_processing_times'])
            }
            
        except Exception as e:
            self.logger.error(f"获取性能统计失败: {e}")
            return {}


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, max_memory_percent: float = 80.0):
        """
        初始化内存管理器
        
        Args:
            max_memory_percent: 最大内存使用百分比
        """
        self.max_memory_percent = max_memory_percent
        self.logger = logging.getLogger(__name__)
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用情况"""
        memory = psutil.virtual_memory()
        return {
            'percent': memory.percent,
            'available_gb': memory.available / (1024**3),
            'used_gb': memory.used / (1024**3),
            'total_gb': memory.total / (1024**3),
            'is_high': memory.percent > self.max_memory_percent
        }
    
    def optimize_memory(self) -> bool:
        """优化内存使用"""
        try:
            initial_memory = psutil.virtual_memory().percent
            
            # 强制垃圾回收
            gc.collect()
            
            # 等待一秒让系统更新内存统计
            time.sleep(1)
            
            final_memory = psutil.virtual_memory().percent
            freed_memory = initial_memory - final_memory
            
            if freed_memory > 0:
                self.logger.info(f"内存优化完成，释放了 {freed_memory:.1f}% 内存")
                return True
            else:
                self.logger.debug("内存优化完成，无明显改善")
                return False
                
        except Exception as e:
            self.logger.error(f"内存优化失败: {e}")
            return False
    
    def should_pause_processing(self) -> bool:
        """判断是否应该暂停处理以释放内存"""
        memory_info = self.check_memory_usage()
        return memory_info['is_high']


class FileUtils:
    """文件操作工具"""
    
    @staticmethod
    def calculate_file_hash(file_path: Path, algorithm: str = 'md5') -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法
            
        Returns:
            str: 文件哈希值
        """
        try:
            hash_func = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception:
            return ""
    
    @staticmethod
    def get_file_info(file_path: Path) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 文件信息
        """
        try:
            stat = file_path.stat()
            return {
                'size': stat.st_size,
                'size_mb': stat.st_size / (1024 * 1024),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'is_readable': os.access(file_path, os.R_OK),
                'is_writable': os.access(file_path, os.W_OK),
                'extension': file_path.suffix.lower(),
                'name': file_path.name
            }
        except Exception:
            return {}
    
    @staticmethod
    def clean_old_files(directory: Path, days: int = 30, pattern: str = "*") -> int:
        """
        清理旧文件
        
        Args:
            directory: 目录路径
            days: 保留天数
            pattern: 文件模式
            
        Returns:
            int: 清理的文件数量
        """
        try:
            cutoff_time = time.time() - (days * 24 * 3600)
            cleaned_count = 0
            
            for file_path in directory.glob(pattern):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    cleaned_count += 1
            
            return cleaned_count
            
        except Exception:
            return 0
    
    @staticmethod
    def ensure_directory(directory: Path) -> bool:
        """
        确保目录存在
        
        Args:
            directory: 目录路径
            
        Returns:
            bool: 是否成功
        """
        try:
            directory.mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False


class DataProcessor:
    """数据处理工具"""
    
    @staticmethod
    def batch_process(items: List[Any], batch_size: int, 
                     processor: Callable[[List[Any]], Any]) -> List[Any]:
        """
        批量处理数据
        
        Args:
            items: 待处理项目列表
            batch_size: 批次大小
            processor: 处理函数
            
        Returns:
            List[Any]: 处理结果列表
        """
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            try:
                result = processor(batch)
                if isinstance(result, list):
                    results.extend(result)
                else:
                    results.append(result)
            except Exception as e:
                logging.error(f"批处理失败: {e}")
        
        return results
    
    @staticmethod
    def filter_recent_items(items: List[Dict[str, Any]], 
                          time_field: str = 'timestamp',
                          hours: int = 24) -> List[Dict[str, Any]]:
        """
        过滤最近的项目
        
        Args:
            items: 项目列表
            time_field: 时间字段名
            hours: 小时数
            
        Returns:
            List[Dict[str, Any]]: 过滤后的项目列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        filtered_items = []
        for item in items:
            try:
                item_time = datetime.fromisoformat(item[time_field].replace('Z', '+00:00'))
                if item_time >= cutoff_time:
                    filtered_items.append(item)
            except (KeyError, ValueError):
                continue
        
        return filtered_items


def timing_decorator(func: Callable) -> Callable:
    """
    计时装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        Callable: 装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            duration = end_time - start_time
            logging.debug(f"{func.__name__} 执行时间: {duration:.2f}秒")
    
    return wrapper


def retry_decorator(max_retries: int = 3, delay: float = 1.0, 
                   exceptions: tuple = (Exception,)) -> Callable:
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟
        exceptions: 需要重试的异常类型
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logging.warning(f"{func.__name__} 失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                    else:
                        logging.error(f"{func.__name__} 重试失败: {e}")
            
            raise last_exception
        
        return wrapper
    return decorator


class SystemInfo:
    """系统信息获取器"""
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                'platform': os.name,
                'cpu_count': psutil.cpu_count(),
                'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {},
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'disk_total_gb': psutil.disk_usage('/').total / (1024**3),
                'python_version': f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
                'process_id': os.getpid(),
                'working_directory': os.getcwd()
            }
        except Exception as e:
            logging.error(f"获取系统信息失败: {e}")
            return {}
    
    @staticmethod
    def get_process_info() -> Dict[str, Any]:
        """获取当前进程信息"""
        try:
            process = psutil.Process()
            return {
                'pid': process.pid,
                'name': process.name(),
                'cpu_percent': process.cpu_percent(),
                'memory_percent': process.memory_percent(),
                'memory_mb': process.memory_info().rss / (1024 * 1024),
                'create_time': datetime.fromtimestamp(process.create_time()),
                'num_threads': process.num_threads(),
                'status': process.status()
            }
        except Exception as e:
            logging.error(f"获取进程信息失败: {e}")
            return {}


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
memory_manager = MemoryManager()


if __name__ == "__main__":
    # 测试工具函数
    print("系统信息:")
    system_info = SystemInfo.get_system_info()
    for key, value in system_info.items():
        print(f"  {key}: {value}")
    
    print("\n进程信息:")
    process_info = SystemInfo.get_process_info()
    for key, value in process_info.items():
        print(f"  {key}: {value}")
    
    print("\n内存信息:")
    memory_info = memory_manager.check_memory_usage()
    for key, value in memory_info.items():
        print(f"  {key}: {value}")
    
    # 测试性能监控
    performance_monitor.start_monitoring(interval=5)
    time.sleep(10)
    performance_monitor.stop_monitoring()
    
    stats = performance_monitor.get_current_stats()
    print("\n性能统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
