#!/usr/bin/env python3
"""
简化版本的Gemini客户端，使用更简单的提示词
"""

import time
import logging
import json
from typing import List, Optional
from enum import Enum
from dataclasses import dataclass
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold


class ContentType(Enum):
    """内容类型枚举"""
    NEWS = "news"
    TECH_ARTICLE = "tech_article"  
    GENERAL_ARTICLE = "general_article"
    UNKNOWN = "unknown"


@dataclass
class SummaryResponse:
    """汇总响应数据类"""
    title: str
    core_facts: str
    key_points: List[str]
    importance_level: str
    importance_reason: str
    keywords: List[str]
    content_type: str
    processing_status: str
    error_message: Optional[str] = None


class GeminiClient:
    """简化版本的Gemini客户端"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp", 
                 retry_attempts: int = 3, retry_delay: int = 5):
        """初始化客户端"""
        self.api_key = api_key
        self.model_name = model_name  
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.logger = logging.getLogger(__name__)
        
        # 配置API
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
        
    def generate_summary(self, content: str, filename: str = "") -> SummaryResponse:
        """生成内容汇总"""
        try:
            # 使用更简单的提示词
            prompt = f"""
请阅读以下内容并提供简要汇总:

请提供:
1. 标题 (一句话)
2. 主要内容 (1-2句话)
3. 3个关键点 (每个一句话)
4. 重要性评级 (高/中/低)
5. 3个关键词

请用简洁的中文回答，不需要JSON格式。

===

{content}

"""

            # 调用API
            response = self.model.generate_content(prompt)
            
            if not response.text:
                raise ValueError("API返回空响应")
            
            # 简单的文本解析
            response_text = response.text.strip()
            lines = [line.strip() for line in response_text.split('\n') if line.strip()]
            
            # 提取信息
            title = "文档汇总"
            core_facts = ""
            key_points = []
            keywords = []
            
            # 简单的文本分析
            if lines:
                title = lines[0][:100]  # 第一行作为标题
                core_facts = ' '.join(lines[:3])[:300]  # 前3行作为核心事实
                key_points = lines[1:4] if len(lines) > 1 else ["无关键点"]  # 第2-4行作为关键点
                keywords = ["文档", "汇总", "内容"]  # 默认关键词
            
            return SummaryResponse(
                title=title,
                core_facts=core_facts,
                key_points=key_points[:3],  # 最多3个关键点
                importance_level="中",
                importance_reason="基于内容分析",
                keywords=keywords[:3],  # 最多3个关键词
                content_type="general_article", 
                processing_status="success"
            )
            
        except Exception as e:
            self.logger.error(f"生成汇总失败: {e}")
            return SummaryResponse(
                title="处理失败",
                core_facts="",
                key_points=[],
                importance_level="低",
                importance_reason="处理过程中发生错误",
                keywords=[],
                content_type="unknown",
                processing_status="error",
                error_message=str(e)
            )