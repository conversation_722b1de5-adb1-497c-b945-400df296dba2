#!/usr/bin/env python3
"""
错误处理和日志系统模块

提供完整的错误处理和恢复机制，包括：
- 分类错误处理
- 重试队列管理
- 错误恢复策略
- 详细日志记录
- 系统健康监控
"""

import logging
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
from queue import Queue, Empty
import threading
import traceback


class ErrorType(Enum):
    """
    错误类型枚举
    
    定义系统中可能出现的各种错误类型，用于错误分类和处理策略选择。
    不同类型的错误会采用不同的处理和重试策略。
    
    Values:
        API_ERROR: API调用相关错误（网络超时、认证失败、配额超限等）
        NETWORK_ERROR: 网络连接错误（DNS解析失败、连接超时等）
        FILE_ERROR: 文件操作错误（文件不存在、权限不足、磁盘满等）
        CONTENT_ERROR: 内容处理错误（编码问题、格式错误、解析失败等）
        SYSTEM_ERROR: 系统级错误（内存不足、权限问题、依赖缺失等）
        UNKNOWN_ERROR: 未知或未分类的错误
    """
    API_ERROR = "api_error"
    NETWORK_ERROR = "network_error"
    FILE_ERROR = "file_error"
    CONTENT_ERROR = "content_error"
    SYSTEM_ERROR = "system_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorRecord:
    """错误记录数据类"""
    error_id: str
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    context: Dict[str, Any]
    timestamp: str
    traceback_info: str
    retry_count: int = 0
    resolved: bool = False
    resolution_time: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['error_type'] = self.error_type.value
        data['severity'] = self.severity.value
        return data


@dataclass
class ProcessingTask:
    """处理任务数据类"""
    task_id: str
    file_path: str
    task_type: str
    priority: int
    created_time: str
    retry_count: int = 0
    max_retries: int = 3
    last_error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, max_retries: int = 3, retry_delay: int = 5, 
                 error_log_file: Optional[str] = None):
        """
        初始化错误处理器
        
        Args:
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            error_log_file: 错误日志文件路径
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.logger = logging.getLogger(__name__)
        
        # 错误记录
        self.error_records: List[ErrorRecord] = []
        self.error_log_file = Path(error_log_file) if error_log_file else None
        
        # 重试队列
        self.retry_queue: Queue = Queue()
        self.retry_thread = None
        self.retry_running = False
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'errors_by_type': {},
            'errors_by_severity': {},
            'resolved_errors': 0,
            'unresolved_errors': 0
        }
        
        # 错误处理策略
        self.error_strategies = {
            ErrorType.API_ERROR: self._handle_api_error,
            ErrorType.NETWORK_ERROR: self._handle_network_error,
            ErrorType.FILE_ERROR: self._handle_file_error,
            ErrorType.CONTENT_ERROR: self._handle_content_error,
            ErrorType.SYSTEM_ERROR: self._handle_system_error,
            ErrorType.UNKNOWN_ERROR: self._handle_unknown_error
        }
        
        # 启动重试处理线程
        self.start_retry_processor()
    
    def handle_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文信息
            
        Returns:
            bool: 是否成功处理错误
        """
        try:
            # 分类错误
            error_type = self._classify_error(error)
            severity = self._assess_severity(error, error_type)
            
            # 创建错误记录
            error_record = ErrorRecord(
                error_id=self._generate_error_id(),
                error_type=error_type,
                severity=severity,
                message=str(error),
                context=context,
                timestamp=datetime.now().isoformat(),
                traceback_info=traceback.format_exc()
            )
            
            # 记录错误
            self._record_error(error_record)
            
            # 根据错误类型选择处理策略
            strategy = self.error_strategies.get(error_type, self._handle_unknown_error)
            success = strategy(error, context, error_record)
            
            if success:
                error_record.resolved = True
                error_record.resolution_time = datetime.now().isoformat()
                self.error_stats['resolved_errors'] += 1
            else:
                self.error_stats['unresolved_errors'] += 1
            
            return success
            
        except Exception as e:
            self.logger.critical(f"错误处理器自身发生错误: {e}")
            return False
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """
        分类错误类型
        
        Args:
            error: 异常对象
            
        Returns:
            ErrorType: 错误类型
        """
        error_str = str(error).lower()
        error_type_name = type(error).__name__.lower()
        
        # API相关错误
        if any(keyword in error_str for keyword in ['api', 'quota', 'rate limit', 'authentication']):
            return ErrorType.API_ERROR
        
        # 网络相关错误
        if any(keyword in error_str for keyword in ['network', 'connection', 'timeout', 'dns']):
            return ErrorType.NETWORK_ERROR
        
        # 文件相关错误
        if any(keyword in error_str for keyword in ['file', 'directory', 'permission', 'not found']):
            return ErrorType.FILE_ERROR
        
        # 内容相关错误
        if any(keyword in error_str for keyword in ['encoding', 'decode', 'parse', 'format']):
            return ErrorType.CONTENT_ERROR
        
        # 系统相关错误
        if any(keyword in error_type_name for keyword in ['memory', 'system', 'os']):
            return ErrorType.SYSTEM_ERROR
        
        return ErrorType.UNKNOWN_ERROR
    
    def _assess_severity(self, error: Exception, error_type: ErrorType) -> ErrorSeverity:
        """
        评估错误严重程度
        
        Args:
            error: 异常对象
            error_type: 错误类型
            
        Returns:
            ErrorSeverity: 错误严重程度
        """
        error_str = str(error).lower()
        
        # 关键错误
        if any(keyword in error_str for keyword in ['critical', 'fatal', 'authentication failed']):
            return ErrorSeverity.CRITICAL
        
        # 高严重性错误
        if error_type in [ErrorType.SYSTEM_ERROR] or 'memory' in error_str:
            return ErrorSeverity.HIGH
        
        # 中等严重性错误
        if error_type in [ErrorType.API_ERROR, ErrorType.NETWORK_ERROR]:
            return ErrorSeverity.MEDIUM
        
        # 低严重性错误
        return ErrorSeverity.LOW
    
    def _handle_api_error(self, error: Exception, context: Dict[str, Any], 
                         record: ErrorRecord) -> bool:
        """处理API错误"""
        error_str = str(error).lower()
        
        # 认证错误 - 不重试
        if 'authentication' in error_str or 'api key' in error_str:
            self.logger.critical("API认证失败，请检查API密钥")
            return False
        
        # 配额超限 - 延迟重试
        if 'quota' in error_str or 'rate limit' in error_str:
            self.logger.warning("API配额超限，添加到重试队列")
            self._add_to_retry_queue(context, delay=300)  # 5分钟后重试
            return True
        
        # 其他API错误 - 正常重试
        self.logger.warning(f"API错误，添加到重试队列: {error}")
        self._add_to_retry_queue(context)
        return True
    
    def _handle_network_error(self, error: Exception, context: Dict[str, Any], 
                            record: ErrorRecord) -> bool:
        """处理网络错误"""
        self.logger.warning(f"网络错误，添加到重试队列: {error}")
        self._add_to_retry_queue(context, delay=60)  # 1分钟后重试
        return True
    
    def _handle_file_error(self, error: Exception, context: Dict[str, Any], 
                         record: ErrorRecord) -> bool:
        """处理文件错误"""
        error_str = str(error).lower()
        
        # 权限错误 - 跳过文件
        if 'permission' in error_str:
            self.logger.warning(f"文件权限错误，跳过文件: {context.get('file_path', 'unknown')}")
            return True
        
        # 文件不存在 - 跳过文件
        if 'not found' in error_str:
            self.logger.warning(f"文件不存在，跳过: {context.get('file_path', 'unknown')}")
            return True
        
        # 其他文件错误 - 重试
        self.logger.warning(f"文件错误，添加到重试队列: {error}")
        self._add_to_retry_queue(context)
        return True
    
    def _handle_content_error(self, error: Exception, context: Dict[str, Any], 
                            record: ErrorRecord) -> bool:
        """处理内容错误"""
        self.logger.warning(f"内容错误，尝试容错处理: {error}")
        # 内容错误通常可以通过容错处理解决
        return True
    
    def _handle_system_error(self, error: Exception, context: Dict[str, Any], 
                           record: ErrorRecord) -> bool:
        """处理系统错误"""
        error_str = str(error).lower()
        
        # 内存错误 - 严重错误
        if 'memory' in error_str:
            self.logger.critical("内存不足，建议重启应用")
            return False
        
        # 其他系统错误 - 延迟重试
        self.logger.error(f"系统错误，延迟重试: {error}")
        self._add_to_retry_queue(context, delay=120)  # 2分钟后重试
        return True
    
    def _handle_unknown_error(self, error: Exception, context: Dict[str, Any], 
                            record: ErrorRecord) -> bool:
        """处理未知错误"""
        self.logger.error(f"未知错误，添加到重试队列: {error}")
        self._add_to_retry_queue(context)
        return True
    
    def _add_to_retry_queue(self, context: Dict[str, Any], delay: int = None) -> None:
        """
        添加任务到重试队列
        
        Args:
            context: 任务上下文
            delay: 延迟时间（秒）
        """
        if delay is None:
            delay = self.retry_delay
        
        task = ProcessingTask(
            task_id=self._generate_task_id(),
            file_path=context.get('file_path', ''),
            task_type=context.get('task_type', 'process_file'),
            priority=context.get('priority', 1),
            created_time=datetime.now().isoformat(),
            max_retries=self.max_retries
        )
        
        # 延迟添加到队列
        def delayed_add():
            time.sleep(delay)
            self.retry_queue.put(task)
            self.logger.info(f"任务已添加到重试队列: {task.file_path}")
        
        threading.Thread(target=delayed_add, daemon=True).start()
    
    def start_retry_processor(self) -> None:
        """启动重试处理器"""
        if self.retry_running:
            return
        
        self.retry_running = True
        self.retry_thread = threading.Thread(target=self._process_retry_queue, daemon=True)
        self.retry_thread.start()
        self.logger.info("重试处理器已启动")
    
    def stop_retry_processor(self) -> None:
        """停止重试处理器"""
        self.retry_running = False
        if self.retry_thread and self.retry_thread.is_alive():
            self.retry_thread.join()
        self.logger.info("重试处理器已停止")
    
    def _process_retry_queue(self) -> None:
        """处理重试队列"""
        while self.retry_running:
            try:
                # 从队列获取任务（超时1秒）
                task = self.retry_queue.get(timeout=1)
                
                # 检查重试次数
                if task.retry_count >= task.max_retries:
                    self.logger.warning(f"任务重试次数超限，放弃: {task.file_path}")
                    continue
                
                # 增加重试次数
                task.retry_count += 1
                
                self.logger.info(f"重试任务 ({task.retry_count}/{task.max_retries}): {task.file_path}")
                
                # 这里应该调用实际的处理函数
                # 由于模块间的依赖关系，这里只是记录日志
                # 实际实现中需要注入处理函数
                
            except Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理重试队列失败: {e}")
    
    def _record_error(self, error_record: ErrorRecord) -> None:
        """记录错误"""
        self.error_records.append(error_record)
        
        # 更新统计
        self.error_stats['total_errors'] += 1
        
        error_type_key = error_record.error_type.value
        self.error_stats['errors_by_type'][error_type_key] = \
            self.error_stats['errors_by_type'].get(error_type_key, 0) + 1
        
        severity_key = error_record.severity.value
        self.error_stats['errors_by_severity'][severity_key] = \
            self.error_stats['errors_by_severity'].get(severity_key, 0) + 1
        
        # 记录到文件
        if self.error_log_file:
            self._write_error_to_file(error_record)
        
        # 根据严重程度选择日志级别
        if error_record.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"严重错误: {error_record.message}")
        elif error_record.severity == ErrorSeverity.HIGH:
            self.logger.error(f"高级错误: {error_record.message}")
        elif error_record.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"中级错误: {error_record.message}")
        else:
            self.logger.info(f"低级错误: {error_record.message}")
    
    def _write_error_to_file(self, error_record: ErrorRecord) -> None:
        """将错误写入文件"""
        try:
            if not self.error_log_file.parent.exists():
                self.error_log_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.error_log_file, 'a', encoding='utf-8') as f:
                json.dump(error_record.to_dict(), f, ensure_ascii=False)
                f.write('\n')
                
        except Exception as e:
            self.logger.error(f"写入错误日志文件失败: {e}")
    
    def _generate_error_id(self) -> str:
        """生成错误ID"""
        return f"ERR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.error_records):04d}"
    
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        return f"TASK_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.retry_queue.qsize():04d}"
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            **self.error_stats,
            'retry_queue_size': self.retry_queue.qsize(),
            'recent_errors': [record.to_dict() for record in self.error_records[-10:]]
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        total_errors = self.error_stats['total_errors']
        critical_errors = self.error_stats['errors_by_severity'].get('critical', 0)
        high_errors = self.error_stats['errors_by_severity'].get('high', 0)
        
        # 计算健康分数
        health_score = 100
        if total_errors > 0:
            health_score -= min(critical_errors * 20, 60)  # 严重错误最多扣60分
            health_score -= min(high_errors * 10, 30)      # 高级错误最多扣30分
            health_score = max(health_score, 0)
        
        # 确定健康状态
        if health_score >= 80:
            status = "healthy"
        elif health_score >= 60:
            status = "warning"
        elif health_score >= 40:
            status = "degraded"
        else:
            status = "critical"
        
        return {
            'status': status,
            'health_score': health_score,
            'total_errors': total_errors,
            'critical_errors': critical_errors,
            'high_errors': high_errors,
            'retry_queue_size': self.retry_queue.qsize(),
            'last_error_time': self.error_records[-1].timestamp if self.error_records else None
        }


if __name__ == "__main__":
    # 测试错误处理器
    error_handler = ErrorHandler(error_log_file="test_errors.log")
    
    # 模拟各种错误
    try:
        raise ValueError("测试API错误: rate limit exceeded")
    except Exception as e:
        error_handler.handle_error(e, {'file_path': 'test.md', 'task_type': 'process_file'})
    
    try:
        raise FileNotFoundError("测试文件错误: file not found")
    except Exception as e:
        error_handler.handle_error(e, {'file_path': 'missing.md', 'task_type': 'process_file'})
    
    # 查看统计信息
    stats = error_handler.get_error_statistics()
    print("错误统计:", json.dumps(stats, indent=2, ensure_ascii=False))
    
    health = error_handler.get_health_status()
    print("健康状态:", json.dumps(health, indent=2, ensure_ascii=False))
    
    # 停止重试处理器
    error_handler.stop_retry_processor()
