#!/usr/bin/env python3
"""
存储和跟踪模块

提供数据存储和文件跟踪功能，包括：
- 已处理文件跟踪
- 汇总结果存储
- 报告生成
- 统计信息管理
"""

import json
import hashlib
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
import os


@dataclass
class SummaryResult:
    """
    汇总结果数据类
    
    存储单个文件汇总处理结果的完整数据结构。
    包含汇总内容、处理状态、文件信息和时间戳等完整信息，
    用于持久化存储和后续的报告生成。
    
    Attributes:
        file_path (str): 源文件完整路径
        title (str): 文档标题
        core_facts (str): 核心事实或主要内容
        key_points (List[str]): 关键要点列表
        importance_level (str): 重要性等级评估
        importance_reason (str): 重要性评估理由
        keywords (List[str]): 关键词标签列表
        content_type (str): 内容类型（news/tech_article/general_article）
        processing_status (str): 处理状态（success/error/skipped等）
        summary_time (str): 汇总生成时间戳
        file_size (int): 源文件大小（字节）
        file_hash (str): 文件内容哈希值（用于去重）
        error_message (Optional[str]): 错误信息（如果处理失败）
    """
    file_path: str
    title: str
    core_facts: str
    key_points: List[str]
    importance_level: str
    importance_reason: str
    keywords: List[str]
    content_type: str
    processing_status: str
    summary_time: str
    file_size: int
    file_hash: str
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SummaryResult":
        """从字典创建实例"""
        return cls(**data)


class ProcessedFileTracker:
    """
    已处理文件跟踪器
    
    维护已处理文件的记录，实现智能去重功能。
    通过文件哈希值跟踪文件内容变化，避免重复处理相同内容的文件，
    同时支持文件内容更新后的重新处理。
    
    核心功能：
    1. 哈希跟踪 - 基于文件内容哈希而非路径跟踪
    2. 变化检测 - 自动检测文件内容是否发生变化
    3. 批量操作 - 支持批量标记和查询操作
    4. 持久化存储 - 将跟踪记录保存到JSON文件
    5. 异常恢复 - 处理文件损坏和格式错误
    
    数据结构：
    {
        "files": {
            "file_path": {
                "hash": "content_hash",
                "processed_time": "timestamp",
                "status": "success/error"
            }
        }
    }
    
    Attributes:
        tracker_file (Path): 跟踪文件路径
        logger (logging.Logger): 日志记录器
        _data (Dict): 内存中的跟踪数据
    """
    
    def __init__(self, tracker_file: Path):
        """
        初始化文件跟踪器
        
        Args:
            tracker_file: 跟踪文件路径
        """
        self.tracker_file = Path(tracker_file)
        self.logger = logging.getLogger(__name__)
        
        # 确保目录存在
        self.tracker_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载已处理文件记录
        self._processed_files = self._load_processed_files()
    
    def _load_processed_files(self) -> Dict[str, Dict[str, Any]]:
        """加载已处理文件记录"""
        if not self.tracker_file.exists():
            return {}
        
        try:
            with open(self.tracker_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.logger.info(f"加载了 {len(data)} 个已处理文件记录")
                return data
        except Exception as e:
            self.logger.error(f"加载处理记录失败: {e}")
            return {}
    
    def _save_processed_files(self) -> None:
        """保存已处理文件记录"""
        try:
            with open(self.tracker_file, 'w', encoding='utf-8') as f:
                json.dump(self._processed_files, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存处理记录失败: {e}")
    
    def get_file_hash(self, file_path: Path) -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件哈希值
        """
        try:
            hasher = hashlib.md5()
            with open(file_path, 'rb') as f:
                # 分块读取，避免大文件内存问题
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {file_path}, {e}")
            return ""
    
    def is_processed(self, file_path: Path) -> bool:
        """
        检查文件是否已被处理
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否已被处理
        """
        file_key = str(file_path.absolute())
        
        if file_key not in self._processed_files:
            return False
        
        # 检查文件是否被修改
        try:
            current_hash = self.get_file_hash(file_path)
            stored_hash = self._processed_files[file_key].get('file_hash', '')
            
            if current_hash != stored_hash:
                self.logger.info(f"文件已修改，需要重新处理: {file_path}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查文件处理状态失败: {e}")
            return False
    
    def mark_processed(self, file_path: Path, summary_result: Optional[SummaryResult] = None) -> None:
        """
        标记文件为已处理
        
        Args:
            file_path: 文件路径
            summary_result: 汇总结果（可选）
        """
        try:
            file_key = str(file_path.absolute())
            file_stat = file_path.stat()
            
            record = {
                'file_path': file_key,
                'file_hash': self.get_file_hash(file_path),
                'file_size': file_stat.st_size,
                'modified_time': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                'processed_time': datetime.now().isoformat(),
                'processing_status': 'success' if summary_result else 'processed'
            }
            
            if summary_result:
                record.update({
                    'title': summary_result.title,
                    'content_type': summary_result.content_type,
                    'importance_level': summary_result.importance_level,
                    'keywords': summary_result.keywords
                })
            
            self._processed_files[file_key] = record
            self._save_processed_files()
            
            self.logger.debug(f"标记文件已处理: {file_path}")
            
        except Exception as e:
            self.logger.error(f"标记文件处理状态失败: {e}")
    
    def mark_failed(self, file_path: Path, error_message: str) -> None:
        """
        标记文件处理失败
        
        Args:
            file_path: 文件路径
            error_message: 错误信息
        """
        try:
            file_key = str(file_path.absolute())
            
            record = {
                'file_path': file_key,
                'file_hash': self.get_file_hash(file_path),
                'processed_time': datetime.now().isoformat(),
                'processing_status': 'failed',
                'error_message': error_message
            }
            
            self._processed_files[file_key] = record
            self._save_processed_files()
            
            self.logger.warning(f"标记文件处理失败: {file_path}, {error_message}")
            
        except Exception as e:
            self.logger.error(f"标记文件失败状态失败: {e}")
    
    def get_processed_files(self) -> Dict[str, Dict[str, Any]]:
        """获取所有已处理文件记录"""
        return self._processed_files.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        total_files = len(self._processed_files)
        successful_files = sum(1 for record in self._processed_files.values() 
                             if record.get('processing_status') == 'success')
        failed_files = sum(1 for record in self._processed_files.values() 
                         if record.get('processing_status') == 'failed')
        
        return {
            'total_processed': total_files,
            'successful': successful_files,
            'failed': failed_files,
            'success_rate': successful_files / total_files if total_files > 0 else 0
        }


class Storage:
    """存储管理器"""
    
    def __init__(self, output_directory: str, processed_file_tracker: str):
        """
        初始化存储管理器
        
        Args:
            output_directory: 输出目录
            processed_file_tracker: 处理记录文件路径
        """
        self.output_directory = Path(output_directory)
        self.processed_tracker = ProcessedFileTracker(Path(processed_file_tracker))
        self.logger = logging.getLogger(__name__)
        
        # 确保输出目录存在
        self.output_directory.mkdir(parents=True, exist_ok=True)
        
        # 统计信息
        self._stats = {
            'total_summaries': 0,
            'api_calls': 0,
            'processing_time': 0,
            'start_time': datetime.now()
        }
    
    def save_summary(self, summary: SummaryResult) -> bool:
        """
        保存汇总结果
        
        Args:
            summary: 汇总结果
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"summary_{timestamp}_{hash(summary.file_path) % 10000:04d}.json"
            output_file = self.output_directory / file_name
            
            # 保存汇总结果
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary.to_dict(), f, ensure_ascii=False, indent=2)
            
            # 更新处理记录
            file_path = Path(summary.file_path)
            if summary.processing_status == 'success':
                self.processed_tracker.mark_processed(file_path, summary)
            else:
                self.processed_tracker.mark_failed(file_path, summary.error_message or "处理失败")
            
            # 更新统计信息
            self._stats['total_summaries'] += 1
            if 'success' in summary.processing_status:
                self._stats['api_calls'] += 1
            
            self.logger.info(f"汇总结果已保存: {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存汇总结果失败: {e}")
            return False
    
    def load_summaries(self, limit: Optional[int] = None) -> List[SummaryResult]:
        """
        加载汇总结果
        
        Args:
            limit: 限制加载数量
            
        Returns:
            List[SummaryResult]: 汇总结果列表
        """
        summaries = []
        
        try:
            # 获取所有汇总文件，按时间排序
            summary_files = sorted(
                self.output_directory.glob("summary_*.json"),
                key=lambda x: x.stat().st_mtime,
                reverse=True
            )
            
            if limit:
                summary_files = summary_files[:limit]
            
            for file_path in summary_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        summaries.append(SummaryResult.from_dict(data))
                except Exception as e:
                    self.logger.error(f"加载汇总文件失败: {file_path}, {e}")
            
            self.logger.info(f"加载了 {len(summaries)} 个汇总结果")
            
        except Exception as e:
            self.logger.error(f"加载汇总结果失败: {e}")
        
        return summaries
    
    def generate_report(self, format_type: str = "markdown") -> str:
        """
        生成汇总报告
        
        Args:
            format_type: 报告格式（markdown/json）
            
        Returns:
            str: 报告内容
        """
        try:
            summaries = self.load_summaries(limit=50)  # 最近50个汇总
            stats = self.get_statistics()
            
            if format_type.lower() == "markdown":
                return self._generate_markdown_report(summaries, stats)
            elif format_type.lower() == "json":
                return self._generate_json_report(summaries, stats)
            else:
                raise ValueError(f"不支持的报告格式: {format_type}")
                
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            return f"报告生成失败: {e}"
    
    def _generate_markdown_report(self, summaries: List[SummaryResult], stats: Dict[str, Any]) -> str:
        """生成Markdown格式报告"""
        report_lines = [
            "# 新闻摘要报告",
            "",
            f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 统计信息",
            "",
            f"- 总处理文件数: {stats['total_processed']}",
            f"- 成功处理: {stats['successful']}",
            f"- 处理失败: {stats['failed']}",
            f"- 成功率: {stats['success_rate']:.1%}",
            f"- API调用次数: {self._stats['api_calls']}",
            "",
            "## 最近汇总",
            ""
        ]
        
        for i, summary in enumerate(summaries[:10], 1):
            report_lines.extend([
                f"### {i}. {summary.title}",
                "",
                f"**文件**: {Path(summary.file_path).name}",
                f"**类型**: {summary.content_type}",
                f"**重要性**: {summary.importance_level}",
                f"**时间**: {summary.summary_time}",
                "",
                f"**核心事实**: {summary.core_facts}",
                "",
                "**关键要点**:",
                ""
            ])
            
            for point in summary.key_points:
                report_lines.append(f"- {point}")
            
            report_lines.extend([
                "",
                f"**关键词**: {', '.join(summary.keywords)}",
                "",
                "---",
                ""
            ])
        
        return "\n".join(report_lines)
    
    def _generate_json_report(self, summaries: List[SummaryResult], stats: Dict[str, Any]) -> str:
        """生成JSON格式报告"""
        report_data = {
            'generated_time': datetime.now().isoformat(),
            'statistics': stats,
            'api_stats': self._stats,
            'recent_summaries': [summary.to_dict() for summary in summaries]
        }
        
        return json.dumps(report_data, ensure_ascii=False, indent=2)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        tracker_stats = self.processed_tracker.get_statistics()
        
        # 计算运行时间
        runtime = datetime.now() - self._stats['start_time']
        
        return {
            **tracker_stats,
            'api_calls': self._stats['api_calls'],
            'total_summaries': self._stats['total_summaries'],
            'runtime_hours': runtime.total_seconds() / 3600,
            'output_directory': str(self.output_directory),
            'summary_files_count': len(list(self.output_directory.glob("summary_*.json")))
        }
    
    def cleanup_old_files(self, days: int = 30) -> int:
        """
        清理旧文件
        
        Args:
            days: 保留天数
            
        Returns:
            int: 清理的文件数量
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            cleaned_count = 0
            
            for file_path in self.output_directory.glob("summary_*.json"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    cleaned_count += 1
            
            self.logger.info(f"清理了 {cleaned_count} 个旧文件")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理旧文件失败: {e}")
            return 0


if __name__ == "__main__":
    # 测试存储功能
    storage = Storage("test_output", "test_processed.json")
    
    # 创建测试汇总结果
    test_summary = SummaryResult(
        file_path="/test/file.md",
        title="测试标题",
        core_facts="测试核心事实",
        key_points=["要点1", "要点2"],
        importance_level="中",
        importance_reason="测试原因",
        keywords=["测试", "关键词"],
        content_type="news",
        processing_status="success",
        summary_time=datetime.now().isoformat(),
        file_size=1024,
        file_hash="test_hash"
    )
    
    # 保存测试
    success = storage.save_summary(test_summary)
    print(f"保存结果: {success}")
    
    # 生成报告测试
    report = storage.generate_report("markdown")
    print("Markdown报告:")
    print(report[:500] + "..." if len(report) > 500 else report)
