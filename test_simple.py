#!/usr/bin/env python3
"""
简单测试API响应内容
"""

import asyncio
import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config import Config
import google.generativeai as genai

async def test_simple_api():
    """直接测试API响应"""
    config = Config.load()
    genai.configure(api_key=config.gemini_api_key)
    model = genai.GenerativeModel("gemini-2.5-flash")
    
    content = "NVIDIA发布了全新的Jetson Thor机器人计算平台，专为通用机器人和物理AI应用设计。该平台集成了强大的AI处理能力，支持实时推理和多传感器数据融合。"
    filename = "NVIDIA Jetson Thor技术发布.md"
    
    prompt = f"""你是一位专业的文档分析师，请仔细阅读以下内容并提供精准的分析汇总。

文档标题：{filename}
内容类型：tech

原始内容：
{content}

请按照以下要求提供分析：
1. **技术标题**：用一句话概括核心技术或产品
2. **技术要点**：2-3句话说明主要技术特性、应用场景或解决方案
3. **关键信息**：
   - 核心技术或功能特性
   - 应用领域或使用场景  
   - 技术优势或创新点
4. **影响评估**：评估技术重要性（突破性/重要/一般）
5. **技术标签**：提取3个最相关的技术关键词

**输出格式要求**：
- 使用简洁明了的中文表达
- 每个要点都要具体且有价值
- 避免重复原文，要做到提炼和概括
- 标签词汇要准确反映内容特征

请开始分析："""

    print("=== 发送的Prompt ===")
    print(prompt)
    print("\n" + "="*80 + "\n")
    
    response = model.generate_content(prompt)
    
    print("=== API响应 ===")
    print(response.text)
    print("\n" + "="*80 + "\n")
    
    # 分析响应结构
    lines = [line.strip() for line in response.text.split('\n') if line.strip()]
    print("=== 响应行分析 ===")
    for i, line in enumerate(lines):
        print(f"{i:2d}: {line}")

if __name__ == "__main__":
    asyncio.run(test_simple_api())