# 新闻摘要程序用户指南

## 目录

1. [安装和配置](#安装和配置)
2. [基本使用](#基本使用)
3. [高级功能](#高级功能)
4. [配置详解](#配置详解)
5. [故障排除](#故障排除)
6. [最佳实践](#最佳实践)

## 安装和配置

### 系统要求

- Python 3.9 或更高版本
- 至少 2GB 可用内存
- 稳定的网络连接（用于API调用）
- Google Gemini API密钥

### 快速安装

1. **下载项目**
   ```bash
   git clone <repository-url>
   cd news_forge_ai_agent
   ```

2. **运行安装脚本**
   ```bash
   chmod +x scripts/install.sh
   ./scripts/install.sh
   ```

3. **配置API密钥**
   ```bash
   # 编辑.env文件
   nano .env
   
   # 设置API密钥
   GEMINI_API_KEY=your_actual_api_key_here
   ```

4. **测试安装**
   ```bash
   ./run.sh --status
   ```

### 获取API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录Google账户
3. 点击"Create API Key"
4. 复制生成的API密钥
5. 将密钥粘贴到`.env`文件中

## 基本使用

### 启动应用

```bash
# 使用启动脚本（推荐）
./run.sh

# 或直接使用Python
python main.py
```

### 常用命令

```bash
# 查看帮助
./run.sh --help

# 手动扫描处理文件
./run.sh --scan

# 查看应用状态
./run.sh --status

# 生成汇总报告
./run.sh --report

# 初始化配置
./run.sh --init
```

### 监控模式

启动监控模式后，应用会：

1. **实时监控**指定目录中的文件变化
2. **自动处理**新增或修改的Markdown文件
3. **生成汇总**并保存到输出目录
4. **显示进度**和处理状态

```bash
# 启动监控模式
./run.sh

# 应用会显示类似输出：
# [INFO] 启动新闻摘要应用...
# [INFO] 文件监控已启动
# [12:30:45] 运行 00:15 | 已处理 5 | 失败 0 | API调用 5
```

### 手动处理

如果不需要实时监控，可以手动扫描处理：

```bash
# 扫描并处理所有未处理的文件
./run.sh --scan

# 输出示例：
# 发现 10 个未处理文件
# 进度: 10/10 (100.0%) - 处理完成: article.md
# 
# 处理完成:
#   成功: 8
#   失败: 1
#   跳过: 1
```

## 高级功能

### 批量处理

应用支持高效的批量处理：

```bash
# 设置并发处理数量
export MAX_CONCURRENT_FILES=10
./run.sh --scan
```

### 自定义配置

```bash
# 使用自定义配置文件
./run.sh --config /path/to/custom_config.ini

# 设置日志级别
./run.sh --log-level DEBUG
```

### 报告生成

```bash
# 生成Markdown报告
./run.sh --report markdown

# 生成JSON报告
./run.sh --report json

# 报告会保存到data/summaries/目录
```

### 性能监控

应用内置性能监控功能：

```bash
# 查看详细状态（包含性能信息）
./run.sh --status

# 输出包含：
# - CPU和内存使用率
# - API响应时间
# - 处理速度统计
# - 错误率信息
```

## 配置详解

### 环境变量配置

在`.env`文件中设置：

```bash
# 必需配置
GEMINI_API_KEY=your_api_key_here

# 可选配置
WATCH_DIRECTORY=/path/to/your/notes
SCAN_INTERVAL_MINUTES=30
MAX_CONCURRENT_FILES=5
LOG_LEVEL=INFO
```

### 详细配置文件

在`data/config.ini`中进行详细配置：

```ini
[API]
# AI模型配置
model_name = gemini-2.0-flash-exp
temperature = 0.3
max_tokens = 2048

[MONITORING]
# 文件监控配置
watch_directory = /Users/<USER>/Nutstore Files/notes/main-obsidian/Cubox
scan_interval_minutes = 30
file_extensions = .md
ignore_hidden_files = true
ignore_temp_files = true

[PROCESSING]
# 处理配置
max_concurrent_files = 5
chunk_size_chars = 4000
retry_attempts = 3
retry_delay_seconds = 5
enable_batch_processing = true

[OUTPUT]
# 输出配置
output_directory = data/summaries
log_directory = logs
log_level = INFO
enable_markdown_reports = true

[ADVANCED]
# 高级配置
enable_real_time_monitoring = true
progress_update_interval = 10
memory_optimization = true
api_rate_limit_per_minute = 60
```

### 配置优化建议

**性能优化：**
- `max_concurrent_files`: 根据系统性能调整（2-10）
- `chunk_size_chars`: 大文件分块大小（4000-8000）
- `memory_optimization`: 启用内存优化

**API优化：**
- `retry_attempts`: API重试次数（3-5）
- `retry_delay_seconds`: 重试延迟（5-10秒）
- `api_rate_limit_per_minute`: 每分钟API调用限制

**监控优化：**
- `scan_interval_minutes`: 扫描间隔（15-60分钟）
- `progress_update_interval`: 进度更新间隔（5-30秒）

## 故障排除

### 常见错误及解决方案

#### 1. API密钥问题

**错误信息：**
```
ERROR: GEMINI_API_KEY 未设置
```

**解决方案：**
```bash
# 检查.env文件
cat .env | grep GEMINI_API_KEY

# 确保API密钥正确设置
echo "GEMINI_API_KEY=your_actual_key" >> .env
```

#### 2. 文件权限问题

**错误信息：**
```
ERROR: 文件访问权限被拒绝
```

**解决方案：**
```bash
# 检查目录权限
ls -la /path/to/watch/directory

# 修改权限
chmod -R 755 /path/to/watch/directory
```

#### 3. 依赖包问题

**错误信息：**
```
ModuleNotFoundError: No module named 'google.generativeai'
```

**解决方案：**
```bash
# 重新安装依赖
pip install -r requirements.txt

# 或使用安装脚本
./scripts/install.sh
```

#### 4. 内存不足

**错误信息：**
```
WARNING: 内存使用过高
```

**解决方案：**
```bash
# 减少并发处理数
export MAX_CONCURRENT_FILES=2

# 启用内存优化
# 在config.ini中设置：
# memory_optimization = true
```

#### 5. API调用失败

**错误信息：**
```
ERROR: API调用失败: rate limit exceeded
```

**解决方案：**
```bash
# 降低API调用频率
# 在config.ini中设置：
# api_rate_limit_per_minute = 30
# retry_delay_seconds = 10
```

### 日志分析

```bash
# 查看应用日志
tail -f logs/news_summarizer.log

# 查看错误日志
tail -f logs/errors.log

# 搜索特定错误
grep "ERROR" logs/news_summarizer.log

# 查看API调用日志
grep "API" logs/news_summarizer.log
```

### 调试模式

```bash
# 启用调试模式
export LOG_LEVEL=DEBUG
./run.sh

# 或在配置文件中设置
# log_level = DEBUG
```

## 最佳实践

### 文件组织

1. **目录结构**
   ```
   监控目录/
   ├── 新闻/
   │   ├── 2024-01-01-重要新闻.md
   │   └── 2024-01-02-科技新闻.md
   ├── 技术文章/
   │   ├── Python教程.md
   │   └── AI技术分析.md
   └── 其他文章/
       └── 生活感悟.md
   ```

2. **文件命名**
   - 使用有意义的文件名
   - 包含日期信息（YYYY-MM-DD格式）
   - 避免特殊字符和空格

### 性能优化

1. **合理设置并发数**
   ```bash
   # 根据系统配置调整
   # 4核CPU: max_concurrent_files = 4-6
   # 8核CPU: max_concurrent_files = 6-10
   ```

2. **监控资源使用**
   ```bash
   # 定期查看状态
   ./run.sh --status
   
   # 监控内存使用
   top -p $(pgrep -f "python main.py")
   ```

3. **定期清理**
   ```bash
   # 清理旧的汇总文件（保留30天）
   find data/summaries -name "*.json" -mtime +30 -delete
   
   # 清理旧的日志文件
   find logs -name "*.log" -mtime +7 -delete
   ```

### 安全建议

1. **保护API密钥**
   ```bash
   # 设置文件权限
   chmod 600 .env
   
   # 不要将.env文件提交到版本控制
   echo ".env" >> .gitignore
   ```

2. **备份重要数据**
   ```bash
   # 定期备份处理记录
   cp data/processed.json data/processed.json.backup
   
   # 备份汇总结果
   tar -czf summaries_backup.tar.gz data/summaries/
   ```

### 监控和维护

1. **定期检查**
   - 每周查看错误日志
   - 监控API使用量
   - 检查磁盘空间

2. **性能调优**
   - 根据实际使用情况调整配置
   - 监控处理速度和成功率
   - 优化文件过滤规则

3. **更新维护**
   - 定期更新依赖包
   - 关注API变更通知
   - 备份重要配置和数据

通过遵循这些最佳实践，您可以确保新闻摘要程序稳定、高效地运行。
