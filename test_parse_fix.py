#!/usr/bin/env python3
"""
测试修复后的JSON解析
"""

import json
import re
from modules.gemini_client import ContentType

def test_parse_response():
    """测试解析函数"""
    
    # 模拟API响应
    response_text = '```json\n{\n    "title": "中国国产x86 CPU首次应用于桌面AI PC",\n    "core_facts": "今天（时间），在中国（地点），一款中国国产x86 CPU（人物/机构/产品）首次被应用于桌面AI PC（事件），标志着中国在挑战Intel、AMD的x86 CPU市场领域迈出重要一步（影响）。该CPU目前在办公应用场景下表现良好（具体表现）。",\n    "key_points": [\n        "中国国产x86 CPU首次应用于桌面AI PC",\n        "该应用标志着中国在x86 CPU领域挑战Intel和AMD的重要一步",\n        "这款CPU在办公应用场景下表现良好"\n    ],\n    "importance_level": "中",\n    "importance_reason": "国产CPU应用是技术进步的体现，并对国内相关产业有一定影响。但目前信息较少，性能及市场表现未知，因此重要性为中等。",\n    "keywords": ["国产CPU", "x86 CPU", "AI PC", "中国芯片", "Intel", "AMD"]\n}\n```'
    
    print("🧪 测试修复后的JSON解析逻辑:")
    print("=" * 60)
    
    try:
        # 首先尝试去除markdown代码块标记
        cleaned_text = response_text.strip()
        print(f"原始文本长度: {len(response_text)}")
        print(f"原始文本前50字符: {repr(response_text[:50])}")
        
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]  # 去除```json
            print("✅ 去除了```json开头")
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]  # 去除```
            print("✅ 去除了```结尾")
        cleaned_text = cleaned_text.strip()
        
        print(f"清理后文本长度: {len(cleaned_text)}")
        print(f"清理后文本前50字符: {repr(cleaned_text[:50])}")
        
        # 尝试直接解析清理后的文本
        try:
            data = json.loads(cleaned_text)
            print("✅ 直接解析JSON成功!")
            print(f"标题: {data.get('title')}")
            print(f"关键点数量: {len(data.get('key_points', []))}")
            return True
        except Exception as e:
            print(f"❌ 直接解析失败: {e}")
            
            # 如果直接解析失败，尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print(f"正则提取的JSON前50字符: {repr(json_str[:50])}")
                data = json.loads(json_str)
                print("✅ 正则解析JSON成功!")
                return True
            else:
                print("❌ 无法找到有效的JSON格式")
                return False
                
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_parse_response()
    print(f"\n解析结果: {'✅ 成功' if success else '❌ 失败'}")