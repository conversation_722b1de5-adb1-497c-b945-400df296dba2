#!/usr/bin/env python3
"""
调试Gemini客户端具体问题
"""

import os
from dotenv import load_dotenv
from modules.gemini_client import GeminiClient

def main():
    # 加载环境变量
    load_dotenv()
    
    # 创建客户端
    client = GeminiClient(
        api_key=os.getenv("GEMINI_API_KEY"),
        model_name="gemini-2.0-flash-exp",
        retry_attempts=3,
        retry_delay=5
    )
    
    # 测试内容
    test_content = """
    # 测试新闻
    
    今日，中国国产x86 CPU首次应用于桌面AI PC，标志着在挑战Intel、AMD的道路上迈出重要一步。
    据悉，这款CPU在办公应用场景下表现良好。
    
    这一突破对于国产芯片产业具有重要意义。
    """
    
    print("🧪 测试Gemini客户端...")
    print(f"测试内容: {test_content.strip()}")
    print("=" * 60)
    
    try:
        # 生成汇总
        result = client.generate_summary(test_content, "测试新闻.md")
        
        print(f"处理状态: {result.processing_status}")
        print(f"标题: {result.title}")
        print(f"核心事实: {result.core_facts}")
        print(f"关键点: {result.key_points}")
        print(f"重要性: {result.importance_level}")
        print(f"关键词: {result.keywords}")
        
        if result.error_message:
            print(f"❌ 错误信息: {result.error_message}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()