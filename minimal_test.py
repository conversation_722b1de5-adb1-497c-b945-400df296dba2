#!/usr/bin/env python3
"""
最小化测试，完全隔离问题
"""

import os
import json
import traceback
from dotenv import load_dotenv
import google.generativeai as genai

def test_minimal():
    """最小化测试"""
    load_dotenv()
    
    try:
        # 配置API
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        model = genai.GenerativeModel("gemini-2.0-flash-exp")
        
        # 最简单的prompt
        prompt = """请用JSON格式回答: 今天天气如何?
        
格式: {"answer": "你的回答"}"""
        
        print("🔍 发送请求...")
        response = model.generate_content(prompt)
        
        print("✅ API调用成功")
        print(f"📄 原始响应: {repr(response.text)}")
        
        # 手动解析
        response_text = response.text.strip()
        
        # 去除markdown标记
        if response_text.startswith('```json'):
            response_text = response_text[7:]
        if response_text.startswith('```'):
            response_text = response_text[3:]
        if response_text.endswith('```'):
            response_text = response_text[:-3]
        response_text = response_text.strip()
        
        print(f"🧹 清理后: {repr(response_text)}")
        
        # 解析JSON
        try:
            data = json.loads(response_text)
            print("✅ JSON解析成功:")
            print(f"   结果: {data}")
            return True
        except Exception as parse_error:
            print(f"❌ JSON解析失败: {type(parse_error).__name__}: {parse_error}")
            print(f"   详细错误: {repr(str(parse_error))}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {type(e).__name__}: {e}")
        print(f"   详细错误: {repr(str(e))}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimal()
    print(f"\n最终结果: {'✅ 成功' if success else '❌ 失败'}")