#!/usr/bin/env python3
"""
API 连接测试脚本
用于诊断Gemini API连接问题
"""

import os
from dotenv import load_dotenv
import google.generativeai as genai

def test_api_connection():
    """测试API连接"""
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ 错误：GEMINI_API_KEY 环境变量未设置")
        return False
    
    print(f"✅ API密钥已加载: {api_key[:20]}...")
    
    try:
        # 配置API
        genai.configure(api_key=api_key)
        print("✅ API配置成功")
        
        # 创建模型
        model = genai.GenerativeModel("gemini-2.0-flash-exp")
        print("✅ 模型创建成功")
        
        # 测试简单请求
        test_prompt = "请用JSON格式回答：你好吗？"
        print(f"🔄 发送测试请求: {test_prompt}")
        
        response = model.generate_content(test_prompt)
        
        if response.text:
            print("✅ API调用成功")
            print(f"📄 响应内容: {response.text[:200]}...")
            print(f"📏 响应长度: {len(response.text)} 字符")
            return True
        else:
            print("❌ API返回空响应")
            return False
            
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        print(f"❌ 错误类型: {type(e).__name__}")
        return False

def test_json_parsing():
    """测试JSON解析"""
    import json
    import re
    
    # 测试的响应文本（从日志中看到的错误）
    test_response = '''
    "title": "测试标题",
    "core_facts": "测试内容",
    "key_points": ["要点1", "要点2"]
    '''
    
    print("\n🧪 测试JSON解析:")
    print(f"原始响应: {test_response}")
    
    try:
        # 尝试提取JSON部分
        json_match = re.search(r'\{.*\}', test_response, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            print(f"提取的JSON: {json_str}")
            data = json.loads(json_str)
            print("✅ JSON解析成功")
            return True
        else:
            print("❌ 无法找到有效的JSON格式")
            return False
    except Exception as e:
        print(f"❌ JSON解析失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始API连接诊断...")
    print("=" * 50)
    
    # 测试API连接
    api_success = test_api_connection()
    
    # 测试JSON解析
    json_success = test_json_parsing()
    
    print("\n" + "=" * 50)
    print("📊 诊断结果:")
    print(f"API连接: {'✅ 正常' if api_success else '❌ 异常'}")
    print(f"JSON解析: {'✅ 正常' if json_success else '❌ 异常'}")
    
    if not api_success:
        print("\n💡 建议:")
        print("1. 检查GEMINI_API_KEY是否正确")
        print("2. 检查网络连接")
        print("3. 检查API配额限制")
        print("4. 尝试使用其他模型名称")